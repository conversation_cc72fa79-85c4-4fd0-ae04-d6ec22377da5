<?php
/**
 * Manual trigger for image processing
 * Updated for new image_defects-only system
 */

include_once "flask_auto_starter.php";

echo "<h2>🚀 Manual Image Processing Trigger</h2>";

// Check if Flask is running (using the enhanced function)
function checkFlask() {
    return [
        'running' => isFlaskRunning(),
        'code' => isFlaskRunning() ? 200 : 0,
        'response' => isFlaskRunning() ? 'Flask is running' : 'Flask not responding'
    ];
}

// Try to start Flask if not running (using enhanced function)
function startFlaskManual() {
    echo "<p>🔄 Attempting to start Flask app...</p>";

    $success = startFlask(); // Use the enhanced function from flask_auto_starter.php

    if ($success) {
        echo "<p style='color: green;'>✅ Flask started successfully!</p>";
        return true;
    } else {
        echo "<p style='color: red;'>❌ Failed to start Flask</p>";
        return false;
    }
}

// Main processing
$flaskResult = checkFlask();

if ($flaskResult['running']) {
    echo "<p style='color: green;'>✅ Flask is already running</p>";

    // Trigger processing
    echo "<p>🔄 Triggering image processing...</p>";
    $processing_success = triggerImageProcessing();

    if ($processing_success) {
        echo "<p style='color: green;'>✅ Image processing triggered successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to trigger image processing</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Flask is not responding</p>";

    if (startFlaskManual()) {
        // Try processing again
        echo "<p>🔄 Triggering image processing...</p>";
        $processing_success = triggerImageProcessing();

        if ($processing_success) {
            echo "<p style='color: green;'>✅ Image processing triggered successfully!</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to trigger image processing</p>";
        }
    }
}

// Show manual instructions
echo "<hr>";
echo "<h3>📋 Manual Instructions:</h3>";
echo "<ol>";
echo "<li><strong>Start Flask manually:</strong><br>";
echo "   Open Command Prompt in <code>C:\\xampp\\htdocs\\userdb</code><br>";
echo "   Run: <code>python \"python function files/app.py\"</code></li>";
echo "<li><strong>Check models:</strong><br>";
echo "   Ensure <code>model/best1.pt</code> exists</li>";
echo "<li><strong>Test processing:</strong><br>";
echo "   Visit: <a href='http://127.0.0.1:5000/process_new_images' target='_blank'>http://127.0.0.1:5000/process_new_images</a></li>";
echo "<li><strong>Check results:</strong><br>";
echo "   Run: <a href='test_upload_flow.php'>test_upload_flow.php</a></li>";
echo "</ol>";

// Check database for pending images
echo "<hr>";
echo "<h3>📊 Database Status:</h3>";

$conn = new mysqli("localhost", "root", "", "userdb");
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
} else {
    // Count pending images (images without annotated paths)
    $result = $conn->query("
        SELECT COUNT(*) as pending_count
        FROM image_uploaded
        WHERE Annotated_Image_Path IS NULL
    ");
    
    if ($result) {
        $row = $result->fetch_assoc();
        $pendingCount = $row['pending_count'];
        
        if ($pendingCount > 0) {
            echo "<p style='color: orange;'>⏳ $pendingCount images pending analysis</p>";
        } else {
            echo "<p style='color: green;'>✅ All images have been analyzed</p>";
        }
    }
    
    // Count total images
    $result = $conn->query("SELECT COUNT(*) as total_count FROM image_uploaded");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>📊 Total images uploaded: " . $row['total_count'] . "</p>";
    }
    
    $conn->close();
}
?>

<style>
pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}
code {
    background-color: #f4f4f4;
    padding: 2px 4px;
    border-radius: 2px;
}
</style>
