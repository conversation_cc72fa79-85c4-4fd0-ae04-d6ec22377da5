@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET & BODY STYLING === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background: rgba(74, 144, 226, 0.03);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}


/* === MAIN CONTENT WRAPPER === */
.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem 4rem 2rem;
}


/* === IMAGE SELECTION CONTAINER === */
.select-container {
  background: white;
  padding: 3rem 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  text-align: center;
  max-width: 900px;
  width: 100%;
  position: relative;
}

.select-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
}


/* === PAGE TITLE === */
.page-title {
  font-size: 2rem;
  color: #1e3a5f;
  margin-bottom: 1rem;
  font-weight: 700;
}


/* === PROJECT NAME DISPLAY === */
#projectNameDisplay {
  font-weight: bold;
  margin-bottom: 20px;
  color: #002b5b;
  font-size: 1rem;
}


/* === SELECTION TOOLS (WRAPPER FOR CHECKBOX & COUNT) === */
.selection-tools {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}


/* === SELECT ALL CONTAINER === */
.select-all-container {
  margin-bottom: 1.5rem;
  background: rgba(74, 144, 226, 0.05);
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  display: inline-block;
  border: 1px solid rgba(74, 144, 226, 0.1);
}

.select-all-container label {
  font-size: 1rem;
  font-weight: 500;
  color: #1e3a5f;
  cursor: pointer;
}


/* === IMAGE GRID CONTAINER === */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}


/* === SINGLE IMAGE BOX === */
.image-box {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  position: relative;
  transition: 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.image-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #4a90e2;
}


/* === IMAGE IN THE BOX === */
.image-box img {
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: 8px;
}


/* === CHECKBOX INSIDE IMAGE BOX === */
.image-box input[type="checkbox"] {
  position: absolute;
  top: 12px;
  left: 12px;
  transform: scale(1.3);
  accent-color: #4a90e2;
}


/* === PROCEED BUTTON === */
.proceed-button {
  background: linear-gradient(135deg, #4a90e2, #002f5f);
  color: white;
  font-weight: 600;
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.proceed-button:hover {
  transform: translateY(-2px);
}


/* RESPONSIVE BEHAVIOUR */
@media (min-width: 1024px) {
  .main-content {
    padding: 8rem 2rem 4rem 2rem;  /* More top space on desktops */
  }
}

/* === MOBILE ONLY (≤767px) — Add extra space below header === */
@media (max-width: 767px) {
  .main-content {
    padding-top: 6rem !important; /* Increase this for more space below header */
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 2rem;
  }
}
