/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET & BASE FONT === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100vh;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
}


/* === SIGNUP SECTION BACKGROUND === */
.signup-section {
  background: linear-gradient(135deg, rgba(30, 58, 95, 0.75), rgba(74, 144, 226, 0.65)),
              url('https://images.unsplash.com/photo-1473968512647-3e447244af8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  
  flex: 1; /* fills the space between header and footer */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* === SIGNUP FORM BOX === */
.signup-box {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.signup-box h2 {
  font-size: 1.5rem;
  color: #1e3a5f;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

/* === ERROR AND SUCCESS MESSAGES === */
.error-messages {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.error-message {
  color: #c53030;
  font-size: 0.875rem;
  margin: 0;
  margin-bottom: 0.25rem;
}

.error-message:last-child {
  margin-bottom: 0;
}

.success-message {
  background: #c6f6d5;
  border: 1px solid #9ae6b4;
  color: #2f855a;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  text-align: center;
}

/* === FORM INPUT FIELDS === */
.form-group {
  margin-bottom: 1rem;
  text-align: left;
}

.form-group input {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  padding: 0.875rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.form-group input::placeholder {
  color: #a0aec0;
}

.form-group input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  transform: translateY(-1px);
}

/* === SIGNUP BUTTON === */
.signup-button {
  width: 100%;
  background: linear-gradient(135deg, #4299e1, #2d5a87);
  color: white;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.signup-button:hover {
  background: linear-gradient(135deg, #63b3ed, #4299e1);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(66, 153, 225, 0.3);
}

/* === LOGIN LINK BELOW FORM === */
.login-link {
  color: #4299e1;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.85rem;
  display: block;
  margin-top: 1rem;
}

.login-link:hover {
  color: #2d5a87;
  text-decoration: underline;
}


/* === RESPONSIVENESS BEHAVIOUR === */

/* === DESKTOP (≥1024px) === */
@media (min-width: 1024px) {
  .signup-box {
    margin-top: 11vh;              /* Shift downward slightly */
    max-width: 420px;              /* Limit width so it looks smaller */
    padding: 1.5rem 1.25rem;       /* Slightly reduced padding */
  }
}

/* === MOBILE ONLY (≤767px) — Sign-up page with extra space under header === */
@media (max-width: 767px) {
  :root {
    --header-h-mobile: 64px;   /* adjust if your mobile header differs */
    --footer-h-mobile: 56px;   /* adjust if your footer differs */
    --extra-top-gap: 60px;     /* extra space below header */
  }

  /* Fill space between header & footer */
  .signup-section {
    min-height: calc(100vh - var(--header-h-mobile) - var(--footer-h-mobile)) !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 0 1rem !important;
    box-sizing: border-box;
  }

  /* Unequal top/bottom space: top spacer is bigger */
  .signup-section::before {
    content: "" !important;
    flex: 1 1 0 !important;
    display: block !important;
    margin-top: var(--extra-top-gap) !important;
  }

  .signup-section::after {
    content: "" !important;
    flex: 1 1 0 !important;
    display: block !important;
  }

  /* Smaller sign-up box on mobile */
  .signup-box {
    max-width: 340px !important;
    width: 100% !important;
    margin: 0 auto !important;
    padding: 1.25rem 1rem !important;
    box-sizing: border-box;
    border-radius: 14px;
  }
}
