
from flask import Flask, request, jsonify
from ultralytics import YOLO
import os, cv2, uuid, time, threading, requests
import mysql.connector
import numpy as np
from functools import lru_cache
from google_drive_helper import Google<PERSON>riveUploader, LocalFallbackUploader

app = Flask(__name__)
UPLOAD_FOLDER = 'static/uploads'
RESULT_FOLDER = 'static/results'
ANNOTATED_FOLDER = 'static/annotated'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULT_FOLDER, exist_ok=True)
os.makedirs(ANNOTATED_FOLDER, exist_ok=True)

DB_CONFIG = {
    'host': 'localhost', 'user': 'root', 'password': '',
    'database': 'userdb', 'charset': 'utf8mb4', 'autocommit': True
}

# Google Drive Configuration
GOOGLE_DRIVE_CONFIG = {
    'credentials_file': 'service_account.json',  # You'll need to provide this
    'annotated_folder_id': '1WMIkiDtbGVN_401O5UwOyZPYkk0aKl9s'  # You'll need to provide this
}

# Initialize uploaders
# Using local storage for annotated images (Google Drive service account has quota limitations)
print("📁 Using local storage for annotated images (reliable and fast)")
drive_uploader = LocalFallbackUploader('../static/annotated')

# Note: To use Google Drive, you would need OAuth authentication instead of service account
# See simple_drive_uploader.py for OAuth implementation

def get_db_connection():
    try:
        return mysql.connector.connect(**DB_CONFIG)
    except Exception as e:
        print("❌ DB Error:", e)
        return None

@lru_cache(maxsize=1)
def load_model():
    return YOLO("../model/best1.pt")

# Removed save_image_result function - now only using image_defects table

def get_maintenance_type(defect_type, defect_area_size=None, bbox_area=None):
    """Determine maintenance type based on defect type and area size"""
    defect_lower = defect_type.lower().replace('_', '-')

    if 'clean' in defect_lower:
        return 'None'
    elif 'bird-drop' in defect_lower or 'dusty' in defect_lower:
        return 'Clean'
    elif 'electrical-damage' in defect_lower:
        return 'Repair'
    elif 'crack' in defect_lower:
        return 'Replace'  # Crack defects always require replacement
    elif 'physical-damage' in defect_lower:
        return 'Replace'  # Physical damage always requires replacement
    else:
        return 'Inspect'

def save_image_defect(image_id, defect_type, freq, bbox_area=None):
    """Save defect information to database with maintenance type"""
    conn = get_db_connection()
    try:
        # Get maintenance type for this defect (with area consideration)
        maintenance_type = get_maintenance_type(defect_type, bbox_area=bbox_area)

        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO image_defects (Image_ID, Defect_Type, Defect_Count, Maintenance_Type)
                VALUES (%s, %s, %s, %s)
            """, (image_id, defect_type, freq, maintenance_type))
            conn.commit()
            print(f"✅ Defect saved: {defect_type} x{freq} → {maintenance_type} → {image_id}")
    finally:
        conn.close()

def save_annotated_image_path(image_id, annotated_path):
    """Save annotated image path to database"""
    conn = get_db_connection()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                UPDATE image_uploaded
                SET Annotated_Image_Path = %s
                WHERE Image_ID = %s
            """, (annotated_path, image_id))
            conn.commit()
            print(f"✅ Annotated image path saved: {image_id} → {annotated_path}")
    except Exception as e:
        print(f"❌ Failed to save annotated path: {e}")
    finally:
        conn.close()

def create_annotated_image(image_path, result, image_id):
    """Create annotated image with bounding boxes and upload to Google Drive"""
    try:
        # Load the model to get class names
        model = load_model()

        # Read original image
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ Failed to read image: {image_path}")
            return None

        # Create a copy for annotation
        annotated_img = img.copy()

        # Draw masks and/or bounding boxes for detected defects
        if result.boxes is not None:
            for i, box in enumerate(result.boxes):
                x1, y1, x2, y2 = map(int, box.xyxy[0])
                confidence = float(box.conf[0])
                cls = int(box.cls[0])
                class_name = model.names[cls]

                # Define colors for different defect types (matching your model)
                class_lower = class_name.lower().replace('_', '-')
                if 'dusty' in class_lower:
                    color = (0, 255, 255)  # Yellow for Dusty
                elif 'bird-drop' in class_lower:
                    color = (0, 165, 255)  # Orange for Bird-drop
                elif 'electrical-damage' in class_lower:
                    color = (255, 0, 0)  # Blue for Electrical-Damage
                elif 'physical-damage' in class_lower:
                    color = (0, 0, 255)  # Red for Physical-Damage
                elif 'clean' in class_lower:
                    color = (0, 255, 0)  # Green for Clean (though we usually skip this)
                else:
                    color = (0, 255, 255)  # Default yellow

                # Draw segmentation mask if available
                if result.masks is not None and i < len(result.masks):
                    mask = result.masks[i].data[0].cpu().numpy()

                    # Resize mask to match image dimensions
                    mask_resized = cv2.resize(mask, (annotated_img.shape[1], annotated_img.shape[0]))

                    # Create colored mask overlay (like your reference image)
                    mask_indices = mask_resized > 0.5
                    overlay = annotated_img.copy()
                    overlay[mask_indices] = color

                    # Blend with original image (lighter transparency for better visibility)
                    alpha = 0.3
                    annotated_img = cv2.addWeighted(annotated_img, 1-alpha, overlay, alpha, 0)

                    # Draw clean contour outline
                    mask_uint8 = (mask_resized > 0.5).astype(np.uint8) * 255
                    contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    cv2.drawContours(annotated_img, contours, -1, color, 2)
                else:
                    # Fallback to bounding box if no mask
                    cv2.rectangle(annotated_img, (x1, y1), (x2, y2), color, 2)

                # Create label with defect type and confidence (matching your format)
                label = f"{class_name} {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                # Draw label background
                cv2.rectangle(annotated_img,
                             (x1, y1 - label_size[1] - 10),
                             (x1 + label_size[0], y1),
                             color, -1)

                # Draw label text (black text on colored background)
                cv2.putText(annotated_img, label, (x1, y1 - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

        # Add title to the image
        defect_count = len(result.boxes) if result.boxes else 0
        title = f"Defect Analysis - {image_id} ({defect_count} defects detected)"
        title_size = cv2.getTextSize(title, cv2.FONT_HERSHEY_SIMPLEX, 1.0, 3)[0]
        cv2.putText(annotated_img, title,
                   (10, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 3)
        cv2.putText(annotated_img, title,
                   (10, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)

        # Convert to bytes with compression for faster loading
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 85]  # Good quality, smaller size
        _, buffer = cv2.imencode('.jpg', annotated_img, encode_params)
        image_bytes = buffer.tobytes()

        # Upload to Google Drive
        filename = f"{image_id}_annotated.jpg"
        file_id = drive_uploader.upload_image(image_bytes, filename)

        if file_id:
            # Get public URL
            if hasattr(drive_uploader, 'get_public_url'):
                public_url = drive_uploader.get_public_url(file_id)
                print(f"✅ Annotated image uploaded: {public_url}")
                return public_url
            else:
                # For local fallback
                return f"static/annotated/{filename}"

        return None

    except Exception as e:
        print(f"❌ Failed to create annotated image: {e}")
        return None

def process_image(image_path, image_id, user_id):
    try:
        model = load_model()
        print(f"🤖 Model loaded. Classes: {model.names}")

        result = model(image_path)[0]

        # Check what type of results we have
        has_boxes = result.boxes is not None and len(result.boxes) > 0
        has_masks = result.masks is not None and len(result.masks) > 0

        print(f"🔍 Model inference complete:")
        print(f"   - Boxes: {len(result.boxes) if has_boxes else 0}")
        print(f"   - Masks: {len(result.masks) if has_masks else 0}")

        img = cv2.imread(image_path)
        if img is None:
            return {"error": "Failed to load image."}

        # Analyze detection results - your model detects Physical-Damage areas
        defect_detections = []
        defect_counts = {}
        defect_areas = {}  # Store bbox areas for each defect type

        # Process all detections from the single model
        if has_boxes:
            for i, box in enumerate(result.boxes):
                cls = int(box.cls[0])
                class_name = model.names[cls]
                confidence = float(box.conf[0])

                # Your model detects multiple defect types (exclude "Clean" panels)
                defect_types = ['bird-drop', 'dusty', 'electrical-damage', 'physical-damage']
                if class_name.lower().replace('_', '-') in defect_types:
                    # Calculate bounding box area for maintenance type decision
                    bbox = box.xyxy[0].tolist()
                    bbox_area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])  # width * height

                    detection_data = {
                        'class': class_name,
                        'confidence': confidence,
                        'bbox': bbox,
                        'bbox_area': bbox_area
                    }

                    # Add mask data if available
                    if has_masks and i < len(result.masks):
                        detection_data['mask'] = result.masks[i].data[0].cpu().numpy()

                    defect_detections.append(detection_data)
                    defect_counts[class_name] = defect_counts.get(class_name, 0) + 1

                    # Store the largest bbox area for each defect type (for maintenance decision)
                    if class_name not in defect_areas or bbox_area > defect_areas[class_name]:
                        defect_areas[class_name] = bbox_area

        # Debug output
        print(f"🔍 Detected {len(defect_detections)} defect areas")
        for detection in defect_detections:
            print(f"   - {detection['class']}: {detection['confidence']:.3f}")

        # Calculate panel statistics
        # Since your model detects damage areas, we need to estimate total panels
        defect = len(defect_detections)

        # Estimate total panels - you can adjust this logic
        if defect > 0:
            # If damage detected, estimate total panels (adjust multiplier as needed)
            total_panels = max(defect * 3, 6)  # Assume 1 defect per 3 panels minimum
        else:
            # If no damage, assume some default number of healthy panels
            total_panels = 12  # Default assumption - adjust as needed

        healthy = total_panels - defect

        print(f"📊 Final counts: Total={total_panels}, Healthy={healthy}, Defect={defect}")

        # ✅ Calculate defective rate (defect / total) * 100, rounded to 2 dp
        defective_rate_num = round((defect / total_panels) * 100, 2) if total_panels > 0 else 0.00
        defective_rate_str = f"{defective_rate_num}%"

        # ✅ No longer saving to image_results table - only using image_defects
        print(f"📊 Summary: Total={total_panels}, Healthy={healthy}, Defect={defect}, Rate={defective_rate_str}")

        # ✅ Clear existing defects for this image first
        conn = get_db_connection()
        if conn:
            try:
                with conn.cursor() as cur:
                    cur.execute("DELETE FROM image_defects WHERE Image_ID = %s", (image_id,))
                    conn.commit()
                print(f"🗑️ Cleared existing defects for {image_id}")
            except Exception as e:
                print(f"⚠️ Failed to clear existing defects: {e}")
            finally:
                conn.close()

        # ✅ Save defect types with area information
        for d_type, count in defect_counts.items():
            bbox_area = defect_areas.get(d_type, None)
            save_image_defect(image_id, d_type, count, bbox_area)

        # ✅ Create and upload annotated image
        print(f"🎨 Creating annotated image for {image_id}...")
        annotated_url = create_annotated_image(image_path, result, image_id)
        if annotated_url:
            save_annotated_image_path(image_id, annotated_url)
        else:
            print(f"⚠️ Failed to create annotated image for {image_id}")

        # ✅ Return with % symbol
        return {
            'Image_ID': image_id,
            'User_ID': user_id,
            'Total': total_panels,
            'Defect': defect,
            'Healthy': healthy,
            'Defective_Rate': defective_rate_str,
            'Defect_Types': defect_counts,
            'Annotated_Image_URL': annotated_url
        }

    except Exception as e:
        print(f"❌ Error: {e}")
        return {"error": str(e)}

@app.route('/analyze', methods=['POST'])
def analyze():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'})
    file = request.files['file']
    user_id = request.form.get('user_id', 'U999')
    image_id = request.form.get('image_id', f"IMG{str(int(time.time()))[-4:]}")

    if file.filename == '':
        return jsonify({'error': 'No selected file'})

    filename = file.filename
    save_path = os.path.join(UPLOAD_FOLDER, filename)
    file.save(save_path)

    result = process_image(save_path, image_id, user_id)
    os.remove(save_path)
    return jsonify(result)

@app.route('/process_new_images', methods=['GET'])
def process_new_images():
    conn = get_db_connection()
    try:
        cur = conn.cursor(dictionary=True)
        # Find images that don't have annotated paths (need processing)
        cur.execute("""
            SELECT Image_ID, Image_Path, User_ID
            FROM image_uploaded
            WHERE Annotated_Image_Path IS NULL
        """)
        new_images = cur.fetchall()

        if not new_images:
            print("🟡 No new images found to process.")
            return jsonify({"status": "No new images to process."})

        for row in new_images:
            image_path = row['Image_Path']
            image_id = row['Image_ID']
            user_id = row['User_ID']

            file_id = None
            if "id=" in image_path:
                file_id = image_path.split("id=")[-1]
            elif "/d/" in image_path:
                file_id = image_path.split("/d/")[1].split("/")[0]

            if not file_id:
                continue

            img_url = f"https://drive.google.com/uc?export=download&id={file_id}"
            img_data = requests.get(img_url).content
            local_path = os.path.join(UPLOAD_FOLDER, f"{image_id}.jpg")
            with open(local_path, 'wb') as f:
                f.write(img_data)

            process_image(local_path, image_id, user_id)
            os.remove(local_path)

        return jsonify({"status": f"✅ Processed {len(new_images)} new image(s)."})

    except Exception as e:
        return jsonify({"error": str(e)})
    finally:
        conn.close()

def auto_process_on_startup():
    time.sleep(2)
    try:
        print("🟢 Auto-processing new images after Flask startup...")
        requests.get("http://127.0.0.1:5000/process_new_images")
    except Exception as e:
        print(f"⚠️ Auto-trigger failed: {e}")

if __name__ == '__main__':
    threading.Thread(target=auto_process_on_startup).start()
    app.run(debug=True)
