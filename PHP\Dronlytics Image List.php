<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../PHP/Dronlytics Login Page.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dronlytics Image List</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Image List.css"> 
  <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>

<!-- === HEADER === -->
<?php include '../PHP/Dronlytics Header After Login.php'; ?>

  <!-- === MAIN CONTENT === -->
  <div class="main-content">
    <div class="upload-list-container">
      <h2 class="page-title">Image List</h2>
      <div class="image-grid" id="imageGrid"></div>

      <button class="btn" id="uploadBtn">Submit</button>
      <p id="uploadStatus"></p>
    </div>
  </div>

  <!-- === FOOTER === -->
  <?php include '../PHP/Dronlytics Footer V2.php'; ?>


  <!-- === JAVASCRIPT SECTION === -->
  <script>
    //GOOGLE DRIVE API CONFIGURATION 
    const CLIENT_ID = '************-sfagq5lr4ckp48hhjun38lrk2v5391f0.apps.googleusercontent.com';
    const FOLDER_ID = '1nqR8J7zqCCVUx50If76g05vE7rZCzT7c';
    
    //USER SESSION VALIDATION
    let USER_ID = sessionStorage.getItem('user_id');
    console.log("👤 User ID from sessionStorage:", USER_ID);

    // Fallback: get user_id from PHP session if not in sessionStorage
    if (!USER_ID) {
      const phpUserId = '<?php echo isset($_SESSION["user_id"]) ? $_SESSION["user_id"] : ""; ?>';
      console.log("👤 User ID from PHP session:", phpUserId);

      if (phpUserId) {
        USER_ID = phpUserId;
        sessionStorage.setItem('user_id', USER_ID);
        console.log("✅ User ID set from PHP session");
      } else {
        alert("User session expired. Please log in again.");
        window.location.href = "../PHP/Dronlytics Login Page.php";
        throw new Error("Missing user_id in both sessionStorage and PHP session");
      }
    }

    // DOM ELEMENTS & IMAGE DATA INITIALIZATION
    const grid = document.getElementById("imageGrid");
    let imageList = [];

    const fromSelectImages = sessionStorage.getItem("selectedImages");
    if (fromSelectImages) {
      const selected = JSON.parse(fromSelectImages);
      imageList = selected.map(dataUrl => ({ dataUrl }));
    } else {
      imageList = JSON.parse(sessionStorage.getItem("finalizedImages") || "[]");
    }
    sessionStorage.setItem("uploadReadyImages", JSON.stringify(imageList));

    // GET PROJECT NAME FROM SESSION STORAGE
    const PROJECT_NAME = sessionStorage.getItem("projectName") || "Untitled Project";

    // IMAGE RENDERING FUNCTION 
    function renderImages() {
      grid.innerHTML = "";
      imageList.forEach((img, i) => {
        const box = document.createElement("div");
        box.className = "image-box";
        box.innerHTML = `
          <img src="${img.dataUrl || img.data}" alt="Image ${i + 1}" />
          <button class="btn" onclick="downloadImage(${i})">Download</button>
          <button class="btn remove-btn" onclick="removeImage(${i})">Remove</button>
        `;
        grid.appendChild(box);
      });
    }

    //IMAGE DOWNLOAD FUNCTIONALITY 
    function downloadImage(index) {
      const link = document.createElement("a");
      link.href = imageList[index].dataUrl || imageList[index].data;
      link.download = `image_${index + 1}.png`;
      link.click();
    }

    // IMAGE REMOVAL FUNCTIONALITY 
    function removeImage(index) {
      imageList.splice(index, 1);
      sessionStorage.setItem("uploadReadyImages", JSON.stringify(imageList));
      renderImages();
    }

    // INITIAL PAGE RENDER 
    renderImages();

    // GOOGLE DRIVE UPLOAD HANDLER
    document.getElementById("uploadBtn").addEventListener("click", () => {
      console.log("🔘 Upload button clicked!");

      const uploadImages = JSON.parse(sessionStorage.getItem("uploadReadyImages") || "[]");
      console.log("📷 Images to upload:", uploadImages.length);

      if (uploadImages.length === 0) {
        alert("No images to upload.");
        return;
      }

      console.log("🚀 Starting Google OAuth process...");

      const tokenClient = google.accounts.oauth2.initTokenClient({
        client_id: CLIENT_ID,
        scope: 'https://www.googleapis.com/auth/drive.file',
        callback: async (response) => {
          console.log("🔑 OAuth callback received:", response);

          if (response.error) {
            console.error("❌ OAuth error:", response.error);
            alert("Authentication failed: " + response.error);
            return;
          }

          const accessToken = response.access_token;
          console.log("✅ Access token received");

          try {
            console.log("🚀 Starting upload process...");
            const lastID = await getLastIMGID();
            console.log("📊 Last ID retrieved:", lastID);

            await uploadToDrive(accessToken, uploadImages, lastID);
            console.log("✅ Upload to Drive completed successfully");

            // Redirect to loading page immediately after upload
            // window.location.href = "../PHP/Dronlytics_Loading_Page.php";

            // 🚀 Trigger Flask processing after all uploads complete
            console.log("🤖 Triggering ML processing after upload completion...");
            try {
              const flaskResponse = await fetch("../PHP/flask_auto_starter.php");
              if (flaskResponse.ok) {
                const flaskResult = await flaskResponse.json();
                console.log("🎯 Flask trigger result:", flaskResult);
                // Redirect to loading page only after Flask finishes
                window.location.href = "../PHP/Dronlytics_Loading_Page.php";
              } else {
                console.warn("⚠️ Flask trigger HTTP error:", flaskResponse.status);
                // Optionally redirect anyway if Flask fails
                window.location.href = "../PHP/Dronlytics_Loading_Page.php";
              }
            } catch (flaskError) {
              console.warn("⚠️ Flask trigger failed (continuing anyway):", flaskError);
              // Optionally redirect anyway if Flask fails
              window.location.href = "../PHP/Dronlytics_Loading_Page.php";
            }

          } catch (error) {
            console.error("❌ Upload failed:", error);
            console.error("Error details:", error.stack);

            // Show user-friendly error message
            document.getElementById("uploadStatus").textContent = "❌ Upload failed. Please try again.";
            alert("Upload failed: " + error.message + "\n\nPlease check your internet connection and try again.");
          }
        }
      });

      tokenClient.requestAccessToken();
    });

    
    // GET LAST IMAGE ID FROM DATABASE
    async function getLastIMGID() {
      try {
        console.log("🔍 Fetching last image ID...");
        const res = await fetch('../PHP/get_last_image_id.php');

        if (!res.ok) {
          throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }

        const data = await res.json();
        console.log("📊 Last ID response:", data);

        if (data.error) {
          throw new Error(`Database error: ${data.error}`);
        }

        const last = data.last_id || "IMG000";
        const numericId = parseInt(last.replace("IMG", ""));
        console.log(`🆔 Last ID: ${last}, Numeric: ${numericId}`);
        return numericId;
      } catch (error) {
        console.error("❌ Error getting last image ID:", error);
        alert("Failed to get last image ID: " + error.message);
        throw error;
      }
    }

    // UPLOAD IMAGES TO GOOGLE DRIVE
    async function uploadToDrive(token, images, lastID) {
      const statusText = document.getElementById("uploadStatus");

      try {
        for (let i = 0; i < images.length; i++) {
          statusText.textContent = `Uploading (${i + 1}/${images.length})...`;
          console.log(`📤 Uploading image ${i + 1}/${images.length}`);

          const newIMGID = "IMG" + String(lastID + i + 1).padStart(3, "0");
          const base64 = (images[i].dataUrl || images[i].data).split(',')[1];
          const byteArray = Uint8Array.from(atob(base64), c => c.charCodeAt(0));
          const blob = new Blob([byteArray], { type: "image/png" });

          const metadata = {
            name: `${newIMGID}.png`,
            mimeType: "image/png",
            parents: [FOLDER_ID]
          };

          const form = new FormData();
          form.append("metadata", new Blob([JSON.stringify(metadata)], { type: "application/json" }));
          form.append("file", blob);

          console.log(`☁️ Uploading to Google Drive...`);
          const driveUploadRes = await fetch("https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart", {
            method: "POST",
            headers: new Headers({ Authorization: "Bearer " + token }),
            body: form
          });

          if (!driveUploadRes.ok) {
            throw new Error(`Google Drive upload failed: ${driveUploadRes.status} ${driveUploadRes.statusText}`);
          }

          const driveData = await driveUploadRes.json();
          if (!driveData.id) {
            throw new Error(`No file ID returned from Google Drive: ${JSON.stringify(driveData)}`);
          }

          const fileId = driveData.id;
          const publicLink = `https://drive.google.com/uc?export=view&id=${fileId}`;
          console.log(`✅ Drive upload successful: ${publicLink}`);

          console.log(`💾 Saving to database...`);
          const dbResponse = await fetch("../PHP/save_file_id.php", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              image_id: newIMGID,
              user_id: USER_ID,
              image_path: publicLink,
              project_name: PROJECT_NAME
            })
          });

          if (!dbResponse.ok) {
            console.error(`⚠️ Database save failed for ${newIMGID}: ${dbResponse.status}`);
            throw new Error(`Database save failed: ${dbResponse.status}`);
          } else {
            const dbResult = await dbResponse.json();
            if (dbResult.status === 'error') {
              console.error(`❌ Database error for ${newIMGID}:`, dbResult.message);
              if (dbResult.error_type === 'invalid_user_id') {
                alert('Session expired or invalid user. Please log in again.');
                window.location.href = '../PHP/Dronlytics Login Page.php';
                return;
              }
              throw new Error(`Database error: ${dbResult.message}`);
            } else {
              console.log(`✅ Database save successful for ${newIMGID}`);
            }
          }
        }
        console.log(`🎉 All ${images.length} images uploaded successfully!`);
        statusText.textContent = `✅ All ${images.length} images uploaded successfully!`;
        // Redirect to loading page after all uploads and saves are complete
        window.location.href = "../PHP/Dronlytics_Loading_Page.php";
      } catch (error) {
        console.error("❌ Error in uploadToDrive:", error);
        statusText.textContent = `❌ Upload failed: ${error.message}`;
        throw error; // Re-throw to be caught by the calling function
      }
    }
  </script>

</body>
</html>

