<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../CSS/Dronlytics Before Login Header.css">
</head>
<body>
  
    <!--- HEADER BEFORE LOGIN --->
    <header>
        <div class="header-container">
            <div class="logo">
                <img src="https://i.imgur.com/gZXDmtz.png" alt="Logo" class="logo-image">
                <div class="logo-text">DRONLYTICS</div>
            </div>
            <nav>
                <div class="nav-links">
                    <a href="../PHP/Dronlytics Landing Page.php">Home</a>
                    <a href="../PHP/Dronlytics About Us.php">About Us</a>
                    <a href="../PHP/Dronlytics Login Page.php" class="login-button">
                        <svg class="login-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                        </svg>
                        Login | Register
                    </a>
                </div>
            </nav>
        </div>
    </header>

    <!-- === Mobile Menu Toggle Button and Overlay === -->
    <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">☰</div>
    <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobileMenu()"></div>

    <!-- === Mobile Sidebar Menu === -->
    <div class="mobile-sidebar" id="mobileSidebar">
        <a href="../PHP/Dronlytics Landing Page.php">Home</a>
        <a href="../PHP/Dronlytics About Us.php">About Us</a>
        <a href="../PHP/Dronlytics Login Page.php">Login | Register</a>
    </div>

    <!-- === JavaScript === -->
    <script>
        // ===== MOBILE MENU TOGGLE WITH OVERLAY =====
        function toggleMobileMenu() {
            const sidebar = document.getElementById("mobileSidebar");
            const overlay = document.getElementById("mobileOverlay");
            const isOpen = sidebar.classList.contains("open");

            if (isOpen) {
                sidebar.classList.remove("open");
                overlay.classList.remove("show");
            } else {
                sidebar.classList.add("open");
                overlay.classList.add("show");
            }
        }

        // ===== CLOSE MOBILE MENU =====
        function closeMobileMenu() {
            document.getElementById("mobileSidebar").classList.remove("open");
            document.getElementById("mobileOverlay").classList.remove("show");
        }

        // ===== CLOSE SIDEBAR IF CLICKED OUTSIDE (ON MOBILE) =====
        document.addEventListener("click", function (e) {
            const sidebar = document.getElementById("mobileSidebar");
            const toggle = document.querySelector(".mobile-menu-toggle");

            if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                sidebar.classList.remove("open");
                document.getElementById("mobileOverlay").classList.remove("show");
            }
        });
    </script>

</body>
</html>
