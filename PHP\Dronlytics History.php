<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: Dronlytics Login Page.php");
    exit();
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'] ?? 'User';

// Database connection
$conn = new mysqli("localhost", "root", "", "userdb");
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if user has uploaded images for navigation logic
$hasUploaded = false;
$uploadCheckResult = $conn->query("SELECT 1 FROM image_uploaded WHERE User_ID = '$user_id' LIMIT 1");
if ($uploadCheckResult && $uploadCheckResult->num_rows > 0) {
    $hasUploaded = true;
}

// Pagination settings
$recordsPerPage = 5;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $recordsPerPage;

// Get sorting parameter
$sortOrder = $_GET['sort'] ?? 'newest';

// Get total count for pagination
$countQuery = "SELECT COUNT(*) as total FROM image_uploaded WHERE User_ID = ?";
$countStmt = $conn->prepare($countQuery);
$countStmt->bind_param("s", $user_id);
$countStmt->execute();
$countResult = $countStmt->get_result();
$totalRecords = $countResult->fetch_assoc()['total'];
$totalPages = ceil($totalRecords / $recordsPerPage);

// Fetch user's uploaded images (now calculating metrics from defects)
$query = "
    SELECT
        iu.Image_ID,
        iu.Project_Name,
        iu.Image_Path,
        iu.Annotated_Image_Path,
        iu.Upload_Timestamp,
        DATE(iu.Upload_Timestamp) as Analysis_Date
    FROM image_uploaded iu
    WHERE iu.User_ID = ?
    ORDER BY iu.Upload_Timestamp " . ($sortOrder === 'oldest' ? 'ASC' : 'DESC') . "
    LIMIT ? OFFSET ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("sii", $user_id, $recordsPerPage, $offset);
$stmt->execute();
$result = $stmt->get_result();

$historyData = [];
while ($row = $result->fetch_assoc()) {
    // Calculate panel metrics from defects for this image
    $defect_query = "SELECT SUM(Defect_Count) as total_defects FROM image_defects WHERE Image_ID = ?";
    $defect_stmt = $conn->prepare($defect_query);
    $defect_stmt->bind_param("s", $row['Image_ID']);
    $defect_stmt->execute();
    $defect_result = $defect_stmt->get_result();
    $defect_data = $defect_result->fetch_assoc();

    // Assume 100 total panels (you can adjust this based on your requirements)
    $total_panels = 100;
    $defect_panels = (int)($defect_data['total_defects'] ?? 0);
    $healthy_panels = max(0, $total_panels - $defect_panels);
    $defective_rate = $total_panels > 0 ? round(($defect_panels / $total_panels) * 100, 2) : 0;

    // Add calculated metrics to row
    $row['Total_Panel_Count'] = $total_panels;
    $row['Healthy_Panel_Count'] = $healthy_panels;
    $row['Defect_Panel_Count'] = $defect_panels;
    $row['Defective_Rate'] = $defective_rate . '%';

    $historyData[] = $row;
    $defect_stmt->close();
}

$stmt->close();
$conn->close();
?>

<?php include '../PHP/Dronlytics Header After Login.php'; ?>

<!-- === HISTORY PAGE === -->
<link rel="stylesheet" href="../CSS/Dronlytics History.css"> 
<div class="container">
  <div class="table-container">
    <div class="table-header">
      <h2>Upload History</h2>
      <div class="sort-controls">
        <label for="sort">Sort by:</label>
        <select id="sort" onchange="changeSorting()">
          <option value="newest" <?php echo $sortOrder === 'newest' ? 'selected' : ''; ?>>Newest First</option>
          <option value="oldest" <?php echo $sortOrder === 'oldest' ? 'selected' : ''; ?>>Oldest First</option>
        </select>
      </div>
    </div>

    <?php if (empty($historyData)): ?>
      <div class="no-data">
        <img src="../image/searchimage.png" alt="No Data" class="no-data-icon">
        <h3>No Upload History Found</h3>
        <p>You haven't uploaded any images yet.</p>
        <a href="../PHP/Dronlytics Images Upload.php" class="upload-btn">Upload Your First Image</a>
      </div>
    <?php else: ?>
      <div class="table-wrapper" id="tableWrapper">
        <table>
          <thead>
            <tr>
              <th>UPLOAD DATE</th>
              <th>IMAGE ID</th>
              <th>PROJECT NAME</th>
              <th>ORIGINAL IMAGE</th>
              <th>ANNOTATED IMAGE</th>
              <th>ANALYSIS STATUS</th>
              <th>DASHBOARD</th>
              <th>OVERVIEW</th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($historyData as $row): ?>
              <tr>
                <td><?php echo date('d/m/Y H:i', strtotime($row['Upload_Timestamp'])); ?></td>
                <td><?php echo htmlspecialchars($row['Image_ID']); ?></td>
                <td><?php echo htmlspecialchars($row['Project_Name'] ?? 'N/A'); ?></td>
                <td>
                  <?php if ($row['Image_Path']): ?>
                    <a href="<?php echo htmlspecialchars($row['Image_Path']); ?>" target="_blank" class="view-link">View Original</a>
                  <?php else: ?>
                    <span style="color: #999;">-</span>
                  <?php endif; ?>
                </td>
                <td>
                  <?php if ($row['Annotated_Image_Path']): ?>
                    <a href="../static/annotated/<?php echo htmlspecialchars($row['Image_ID']); ?>_annotated.jpg" target="_blank" class="view-link">View Annotated</a>
                  <?php else: ?>
                    <span style="color: #999;">-</span>
                  <?php endif; ?>
                </td>
                <td>
                  <?php if ($row['Analysis_Date']): ?>
                    <span class="status-analyzed">ANALYZED</span>
                    <div class="analysis-date"><?php echo date('d/m/Y', strtotime($row['Analysis_Date'])); ?></div>
                  <?php else: ?>
                    <span class="status-pending">PENDING</span>
                  <?php endif; ?>
                </td>
                <td>
                  <?php if ($row['Analysis_Date']): ?>
                    <a href="../PHP/dashboard_mysql.php?image_id=<?php echo urlencode($row['Image_ID']); ?>" class="action-link dashboard-link" title="Dashboard for <?php echo htmlspecialchars($row['Image_ID']); ?>">View Dashboard</a>
                  <?php else: ?>
                    <span style="color: #999;">Analysis Required</span>
                  <?php endif; ?>
                </td>
                <td>
                  <?php if ($row['Analysis_Date']): ?>
                    <a href="../PHP/overview.php?image_id=<?php echo urlencode($row['Image_ID']); ?>" class="action-link overview-link" title="Overview for <?php echo htmlspecialchars($row['Image_ID']); ?>">View Overview</a>
                  <?php else: ?>
                    <span style="color: #999;">Analysis Required</span>
                  <?php endif; ?>
                </td>
              </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>

      <!-- Pagination Controls -->
      <?php if ($totalPages > 1): ?>
        <div class="pagination-container">
          <div class="pagination-controls">
            <?php if ($page > 1): ?>
              <a href="?page=1&sort=<?php echo $sortOrder; ?>" class="pagination-btn">First</a>
              <a href="?page=<?php echo ($page - 1); ?>&sort=<?php echo $sortOrder; ?>" class="pagination-btn">‹ Prev</a>
            <?php endif; ?>

            <?php
            // Show page numbers
            $startPage = max(1, $page - 2);
            $endPage = min($totalPages, $page + 2);

            for ($i = $startPage; $i <= $endPage; $i++):
            ?>
              <a href="?page=<?php echo $i; ?>&sort=<?php echo $sortOrder; ?>"
                 class="pagination-btn <?php echo ($i == $page) ? 'active' : ''; ?>">
                <?php echo $i; ?>
              </a>
            <?php endfor; ?>

            <?php if ($page < $totalPages): ?>
              <a href="?page=<?php echo ($page + 1); ?>&sort=<?php echo $sortOrder; ?>" class="pagination-btn">Next ›</a>
              <a href="?page=<?php echo $totalPages; ?>&sort=<?php echo $sortOrder; ?>" class="pagination-btn">Last</a>
            <?php endif; ?>
          </div>
        </div>
      <?php endif; ?>
    <?php endif; ?>
  </div>
</div>

<!-- === JAVASCRIPT === -->
<script>
function changeSorting() {
  const sortValue = document.getElementById('sort').value;
  const currentUrl = new URL(window.location);
  currentUrl.searchParams.set('sort', sortValue);
  currentUrl.searchParams.set('page', '1'); // Reset to first page
  window.location.href = currentUrl.toString();
}
</script>

<!-- === FOOTER === -->
<?php include '../PHP/Dronlytics Footer V2.php'; ?>
