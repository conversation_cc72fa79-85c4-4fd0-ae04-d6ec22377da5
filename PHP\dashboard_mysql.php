<?php
// dashboard_mysql.php – Enhanced dashboard with annotated images and defect analysis

// Start session for non-API requests
if (!isset($_GET['api'])) {
    session_start();
    // Check if user is logged in for HTML page access
    if (!isset($_SESSION['user_id'])) {
        header("Location: ../PHP/login.php");
        exit();
    }
}

// If not API request, show the dashboard HTML
// Start session and get user ID for the HTML page
if (!isset($_SESSION)) {
    session_start();
}
$dashboard_user_id = $_SESSION['user_id'] ?? 'U001'; // Get from session or default
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar Panel Defect Analysis Dashboard</title>
    <link rel="stylesheet" href="../CSS/Dronlytics Header After Login.css">
    <link rel="stylesheet" href="../CSS/dashboard.css">
    <link rel="stylesheet" href="../CSS/Dronlytics Footer V2.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body style="padding-top: 80px;">

  <!-- Arrow to overview -->
  <div style="position: fixed; top: 50%; left: 30px; transform: translateY(-50%); z-index: 1000;">
    <button id="toOverviewBtn" title="Go to Overview" style="background: #4a90e2; color: #fff; border: none; border-radius: 50%; width: 48px; height: 48px; font-size: 2rem; box-shadow: 0 2px 8px rgba(0,0,0,0.12); cursor: pointer; display: flex; align-items: center; justify-content: center; transition: background 0.2s;">
      &larr;
    </button>
  </div>

    <?php include '../PHP/Dronlytics Header After Login.php'; ?>

    <div class="container">
        <div class="header-bar">
            <h1>Solar Panel Defect Detection Dashboard</h1>
            <div class="date"><span id="currentDate">Tuesday, July 22, 2025</span><br><small>TODAY'S DATE</small></div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="imageFilter">Image Selection</label>
                    <select id="imageFilter">
                        <option value="">All Images</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="projectSearch">Project Name Search</label>
                    <div class="search-container">
                        <input type="text" id="projectSearch" placeholder="Search Project Name...">
                        <span class="search-icon">🔍</span>
                    </div>
                </div>

                <div class="filter-group">
                    <label for="dateFilter">Date Filter</label>
                    <input type="date" id="dateFilter">
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="apply-btn" onclick="refreshDashboard()">🔄 Reset Data</button>
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="pdf-download-btn" onclick="downloadPDF()" id="pdfDownloadBtn">
                        📄 Download PDF
                    </button>
                </div>
            </div>
        </div>

        <!-- Metrics Section -->
        <div class="metrics-grid" id="metricsGrid">
            <div class="metric-card common-defect">
                <h3>Common Defect Type</h3>
                <div class="metric-value" id="commonDefectType">-</div>
            </div>
            <div class="metric-card defect">
                <h3>Defect Count</h3>
                <div class="metric-value" id="defectCount">-</div>
            </div>
            <div class="metric-card maintenance">
                <h3>Maintenance Required</h3>
                <div class="metric-value" id="maintenanceRequired">-</div>
            </div>
            <div class="metric-card common-maintenance">
                <h3>Common Maintenance Type</h3>
                <div class="metric-value" id="commonMaintenanceType">-</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Image Section - Top Left -->
            <div class="image-section">
                <div class="image-container">
                    <h3 class="container-title">Processed Image</h3>
                    <div class="image-content">
                        <div id="imageContent">
                            <!-- Empty content -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Defect Summary Table - Top Right -->
            <div class="chart-container defect-summary-section">
                <div class="chart-title">Defect Summary</div>
                <div class="chart-subtitle">Defect Types & Suggested Maintenance</div>
                <div class="defect-summary-table" id="defectSummaryTable">
                    <table>
                        <thead>
                            <tr>
                                <th>Defect Type</th>
                                <th>Defect Count</th>
                                <th>Maintenance Type</th>
                            </tr>
                        </thead>
                        <tbody id="defectSummaryBody">
                            <tr>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Defect Frequency (Bar Chart) - Bottom Left -->
            <div class="chart-container defect-frequency-section">
                <div class="chart-title">Defect Frequency</div>
                <div class="chart-subtitle">By Defect Type</div>
                <canvas id="defectFrequencyChart" class="chart-canvas"></canvas>
            </div>

            <!-- Defect Distribution (Pie Chart) - Bottom Right -->
            <div class="chart-container defect-distribution-section">
                <div class="chart-title">Defect Distribution</div>
                <div class="chart-subtitle">Percentage Breakdown</div>
                <canvas id="defectDistributionChart" class="chart-canvas"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Arrow button: Go to overview for current image
        document.addEventListener('DOMContentLoaded', function() {
            const btn = document.getElementById('toOverviewBtn');
            btn.addEventListener('click', function() {
                // Get selected image ID
                const imageId = document.getElementById('imageFilter').value;
                let url = '../PHP/overview.php';
                if (imageId) {
                    url += '?image_id=' + encodeURIComponent(imageId);
                }
                window.open(url, '_blank');
            });
        });
        let charts = {};
        let currentData = null;

        // Get URL parameters
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM loaded, initializing dashboard...");

            // Test if elements exist
            const testElements = ["defectCount", "commonDefectType", "commonMaintenanceType", "maintenanceRequired"];
            testElements.forEach(id => {
                const el = document.getElementById(id);
                console.log(`Element ${id}:`, el ? "found" : "NOT FOUND");
            });

            updateDate();
            loadDashboard();
            setupEventListeners();
        });

        // Set current date
        function updateDate() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            const formattedDate = now.toLocaleDateString('en-US', options);

            document.getElementById('currentDate').textContent = formattedDate;
        }

        function setupEventListeners() {
            document.getElementById('imageFilter').addEventListener('change', function() {
                console.log('Image filter changed to:', this.value);
                loadDashboard(true); // Preserve selection when filtering
            });

            document.getElementById('projectSearch').addEventListener('input', debounce(function() {
                console.log('Project search changed to:', this.value);
                // Don't reset image selection - let the auto-selection logic handle it
                loadDashboard(false);
            }, 500));

            document.getElementById('dateFilter').addEventListener('change', function() {
                console.log('Date filter changed to:', this.value);
                // Don't reset image selection - let the auto-selection logic handle it
                loadDashboard(false);
            });
        }

        // Debounce function for search input
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Enhanced reset function with better clearing and reloading
function refreshDashboard() {
    console.log('🔄 Reset Data button clicked - clearing all filters');

    // Get the reset button for visual feedback
    const resetButton = event.target;
    const originalText = resetButton.innerHTML;

    try {
        // Show loading state on button
        resetButton.innerHTML = '🔄 Resetting...';
        resetButton.disabled = true;

        // Clear image filter (reset to "All Images")
        const imageFilter = document.getElementById('imageFilter');
        if (imageFilter) {
            imageFilter.value = '';
            imageFilter.selectedIndex = 0; // Ensure "All Images" is selected
            console.log('✅ Image filter cleared');
        }

        // Clear project search
        const projectSearch = document.getElementById('projectSearch');
        if (projectSearch) {
            projectSearch.value = '';
            console.log('✅ Project search cleared');
        }

        // Clear date filter
        const dateFilter = document.getElementById('dateFilter');
        if (dateFilter) {
            dateFilter.value = '';
            console.log('✅ Date filter cleared');
        }

        // Remove any image_id parameter from URL
        if (window.location.search.includes('image_id')) {
            const url = new URL(window.location);
            url.searchParams.delete('image_id');
            window.history.replaceState({}, '', url);
            console.log('✅ Removed image_id from URL');
        }

        console.log('🔄 Reloading dashboard with cleared filters...');

        // Force a full reload of the dashboard data
        loadDashboard(false);

        // Reset button state after a short delay
        setTimeout(() => {
            resetButton.innerHTML = originalText;
            resetButton.disabled = false;
            console.log('✅ Dashboard reset completed');
        }, 1000);

    } catch (error) {
        console.error('❌ Error during dashboard reset:', error);
        // Reset button state on error
        resetButton.innerHTML = originalText;
        resetButton.disabled = false;
    }
}

        async function loadDashboard(preserveSelection = false) {
            try {
                showLoading();

                // Get user ID from PHP session
                const userId = "<?php echo $dashboard_user_id; ?>";
                if (!userId || userId === '') {
                    showError('User session expired. Please log in again.');
                    setTimeout(() => {
                        window.location.href = '../PHP/Dronlytics Login Page.php';
                    }, 2000);
                    return;
                } 

                // Get filter values
                let selectedImage = document.getElementById('imageFilter').value;
                const projectSearch = document.getElementById('projectSearch').value.toLowerCase().trim();
                const dateFilter = document.getElementById('dateFilter').value;

                // Check for URL parameter on first load
                const urlImageId = getUrlParameter('image_id');
                if (urlImageId && !preserveSelection) {
                    selectedImage = urlImageId;
                    console.log('🔍 Using URL image_id parameter:', urlImageId);
                }

                console.log('Loading dashboard with filters:', { selectedImage, projectSearch, dateFilter });

                // Fetch data from server (same as overview.php)
                const url = new URL("../PHP/get_overview_data.php", window.location.href);
                url.searchParams.set("user", userId);

                const response = await fetch(url);
                const data = await response.json();

                if (data.error) {
                    showError(data.error);
                    return;
                }

                console.log('Loaded data:', data);

                // Populate image dropdown if first load
                if (!preserveSelection) {
                    populateImageDropdown(data);
                }

                // URL parameter is already handled above, just update the dropdown
                if (urlImageId && !preserveSelection) {
                    const imageSelect = document.getElementById('imageFilter');
                    imageSelect.value = urlImageId;
                    console.log('✅ Updated dropdown to match URL parameter:', urlImageId);
                }

                // Apply filters step by step (client-side filtering like overview.php)
                let filteredData = [...data]; // Create a copy

                // Filter by project search first
                if (projectSearch) {
                    filteredData = filteredData.filter(row =>
                        row.Project_Name && row.Project_Name.toLowerCase().includes(projectSearch)
                    );
                    console.log('After project filter:', filteredData);
                }

                // Filter by date
                if (dateFilter) {
                    filteredData = filteredData.filter(row => row.Analysis_Date === dateFilter);
                    console.log('After date filter:', filteredData);
                }

                // Filter by selected image (this should be last)
                if (selectedImage) {
                    filteredData = filteredData.filter(row => row.Image_ID === selectedImage);
                    console.log('After image filter:', filteredData);
                }

                console.log('Final filtered data:', filteredData);

                // Determine which image to show
                const imageToShow = determineImageToShow(selectedImage, projectSearch, dateFilter, filteredData);

                // Fetch defect types data (same as overview.php)
                const defectTypesData = await fetchDefectTypesData(filteredData);
                console.log('Defect types data:', defectTypesData);

                // Update dashboard with filtered data
                updateDashboardWithFiltered(filteredData, defectTypesData, imageToShow);

            } catch (error) {
                console.error('Dashboard loading error:', error);
                showError('Failed to load dashboard data');
            }
        }

        function determineImageToShow(selectedImage, projectSearch, dateFilter, filteredData) {
            // Priority 1: If user explicitly selected an image, use that
            if (selectedImage && selectedImage !== '') {
                console.log('Using explicitly selected image:', selectedImage);
                return selectedImage;
            }

            // Priority 2: If filtering by project or date, show first available image
            if ((projectSearch || dateFilter) && filteredData.length > 0) {
                // Sort by Image_ID to get consistent "first" image
                const sortedData = filteredData.sort((a, b) => a.Image_ID.localeCompare(b.Image_ID));
                const firstImage = sortedData[0].Image_ID;

                console.log(`Auto-selecting first image for ${projectSearch ? 'project' : 'date'} filter:`, firstImage);

                // Update the dropdown to reflect this selection
                const imageSelect = document.getElementById('imageFilter');
                imageSelect.value = firstImage;

                return firstImage;
            }

            // Priority 3: No specific image (show default search image like overview.php)
            console.log('No specific image to show - will use default search image');
            return null;
        }

        function populateImageDropdown(data) {
            const imageSelect = document.getElementById('imageFilter');
            const currentValue = imageSelect.value; // Preserve current selection

            // Clear existing options except "All Images"
            while (imageSelect.options.length > 1) {
                imageSelect.removeChild(imageSelect.lastChild);
            }

            // Get unique images from data that have results (same as overview.php)
            const uniqueImageIds = [...new Set(data.map(row => row.Image_ID))].filter(Boolean);

            // Sort the image IDs to ensure proper numerical order (IMG001, IMG002, etc.)
            uniqueImageIds.sort((a, b) => {
                // Extract the numeric part for proper sorting
                const numA = parseInt(a.replace(/\D/g, '')) || 0;
                const numB = parseInt(b.replace(/\D/g, '')) || 0;
                return numA - numB;
            });

            console.log('Populating dropdown with sorted images:', uniqueImageIds);

            uniqueImageIds.forEach(imageId => {
                const option = document.createElement('option');
                option.value = imageId;
                option.textContent = imageId;
                imageSelect.appendChild(option);
            });

            // Restore selection if it still exists
            if (currentValue && uniqueImageIds.includes(currentValue)) {
                imageSelect.value = currentValue;
            }

            // Check for image_id parameter from URL and auto-select it
            const urlImageId = getUrlParameter('image_id');
            if (urlImageId && uniqueImageIds.includes(urlImageId)) {
                console.log('Auto-selecting image from URL parameter:', urlImageId);
                imageSelect.value = urlImageId;
            }
        }

        function updateDashboardWithFiltered(filteredImages, defectTypesApiData, imageToShow) {
            console.log("updateDashboardWithFiltered called with:", { filteredImages, defectTypesApiData, imageToShow });

            // Check if "All Images" is selected (no specific image)
            const selectedImage = document.getElementById('imageFilter').value;

            if (!selectedImage || selectedImage === '') {
                // Show blank metrics when "All Images" is selected
                showBlankMetrics();
                showBlankCharts();
                updateAnnotatedImage(filteredImages, imageToShow);
                return;
            }

            // Calculate metrics from filtered data
            const summary = calculateSummaryFromFiltered(filteredImages);
            console.log("Calculated summary:", summary);

            // Extract defect types from API response (same as overview.php)
            const defectTypes = defectTypesApiData.defect_types || [];
            console.log("Defect types:", defectTypes);

            // Update dashboard components
            updateMetrics(summary, defectTypes);
            updateAnnotatedImage(filteredImages, imageToShow);
            updateCharts({
                defect_frequency: defectTypes,
                defect_distribution: defectTypes
            });
        }

        function calculateSummaryFromFiltered(filteredImages) {
            let totalPanels = 0;
            let healthyPanels = 0;
            let defectivePanels = 0;

            filteredImages.forEach(image => {
                // Ensure no leading zeros by parsing as integers
                totalPanels += parseInt(image.Total_Panel_Count, 10) || 0;
                healthyPanels += parseInt(image.Healthy_Panel_Count, 10) || 0;
                defectivePanels += parseInt(image.Defect_Panel_Count, 10) || 0;
            });

            const defectiveRate = totalPanels > 0 ? ((defectivePanels / totalPanels) * 100).toFixed(2) : 0;
            const maintenanceRequired = defectivePanels > 0 ? 'Yes' : 'No';

            let severityLevel = 'Low';
            if (defectivePanels > 30) {
                severityLevel = 'High';
            } else if (defectivePanels > 10) {
                severityLevel = 'Medium';
            }

            return {
                total_panels: totalPanels,
                healthy_panels: healthyPanels,
                defective_panels: defectivePanels,
                defective_rate: defectiveRate,
                maintenance_required: maintenanceRequired,
                severity_level: severityLevel
            };
        }



        function updateMetrics(summary, defectTypesData) {
            console.log("updateMetrics called with:", { summary, defectTypesData });

            // Calculate defect count from defect types data (actual defect count)
            let totalDefects = 0;
            if (defectTypesData && defectTypesData.length > 0) {
                totalDefects = defectTypesData.reduce((sum, defect) => sum + (parseInt(defect.Total_Count) || 0), 0);
            }

            // Calculate maintenance required (same logic as overview)
            let maintenanceRequired = "No";
            if (totalDefects > 0) {
                maintenanceRequired = "Yes";
            }

            // Get common defect type and maintenance type
            let commonDefectType = "-";
            let commonMaintenanceType = "-";

            if (defectTypesData && defectTypesData.length > 0) {
                // Sort by frequency to get the most common defect
                defectTypesData.sort((a, b) => b.Total_Count - a.Total_Count);
                commonDefectType = defectTypesData[0].Defect_Type;

                // Use maintenance type from API response or calculate it
                commonMaintenanceType = defectTypesData[0].Maintenance_Type || getMaintenanceTypeForDefect(commonDefectType);
            }

            // Update metric displays
            const defectCountEl = document.getElementById("defectCount");
            const commonDefectTypeEl = document.getElementById("commonDefectType");
            const commonMaintenanceTypeEl = document.getElementById("commonMaintenanceType");
            const maintenanceRequiredEl = document.getElementById("maintenanceRequired");

            if (defectCountEl) {
                // Ensure no leading zeros by converting to number first
                const cleanDefectCount = parseInt(totalDefects) || 0;
                defectCountEl.textContent = cleanDefectCount.toString();
                console.log("Updated defectCount:", cleanDefectCount);
            } else {
                console.error("defectCount element not found");
            }

            if (commonDefectTypeEl) {
                commonDefectTypeEl.textContent = commonDefectType;
                console.log("Updated commonDefectType:", commonDefectType);
            } else {
                console.error("commonDefectType element not found");
            }

            if (commonMaintenanceTypeEl) {
                commonMaintenanceTypeEl.textContent = commonMaintenanceType;
                console.log("Updated commonMaintenanceType:", commonMaintenanceType);
            } else {
                console.error("commonMaintenanceType element not found");
            }

            if (maintenanceRequiredEl) {
                maintenanceRequiredEl.textContent = maintenanceRequired;
                console.log("Updated maintenanceRequired:", maintenanceRequired);
            } else {
                console.error("maintenanceRequired element not found");
            }

            console.log("Updated dashboard metrics:", {
                defectCount: totalDefects,
                commonDefectType,
                commonMaintenanceType,
                maintenanceRequired
            });
        }

        function getMaintenanceTypeForDefect(defectType) {
            if (!defectType || defectType === "-") return "-";

            const defectLower = defectType.toLowerCase().replace('_', '-');

            if (defectLower.includes('clean')) {
                return 'None';
            } else if (defectLower.includes('bird-drop') || defectLower.includes('dusty')) {
                return 'Clean';
            } else if (defectLower.includes('electrical-damage')) {
                return 'Repair';
            } else if (defectLower.includes('crack')) {
                return 'Replace';
            } else if (defectLower.includes('physical-damage')) {
                return 'Replace';  // Fixed: Physical-damage now requires Replace
            } else {
                return 'Inspect';
            }
        }

        async function fetchDefectTypesData(validData) {
            try {
                // Get current filter values
                const selectedImage = document.getElementById("imageFilter").value;
                const projectSearch = document.getElementById("projectSearch").value.trim();
                const dateFilter = document.getElementById("dateFilter").value;

                // Build query parameters
                const params = new URLSearchParams({
                    api: 'defect_types'
                });

                if (selectedImage && selectedImage !== '') {
                    params.append('image_id', selectedImage);
                }
                if (projectSearch) {
                    params.append('project', projectSearch);
                }
                if (dateFilter) {
                    params.append('date', dateFilter);
                }

                const response = await fetch(`../PHP/overview_api.php?${params.toString()}`);
                const data = await response.json();

                return data;
            } catch (error) {
                console.error("Error fetching defect types:", error);
                return { defect_types: [], common_maintenance_type: "-" };
            }
        }

        function updateAnnotatedImage(images, imageToShow = null) {
            const imageContent = document.getElementById('imageContent');

            // Check if "All Images" is selected (no specific image)
            const selectedImage = document.getElementById('imageFilter').value;

            if (!selectedImage || selectedImage === '') {
                // Show default search image when "All Images" is selected (like overview.php)
                console.log("Showing default search image for 'All Images' selection");
                showDefaultSearchImage();
                return;
            }

            if (images.length === 0) {
                imageContent.innerHTML = '<div class="loading">No annotated images available for current filters</div>';
                return;
            }

            // Determine which image to display
            let imageToDisplay = null;

            if (imageToShow) {
                // Use specified image
                imageToDisplay = images.find(img => img.Image_ID === imageToShow);
            }

            if (!imageToDisplay && images.length > 0) {
                // Fall back to first available image
                imageToDisplay = images[0];
            }

            if (imageToDisplay && imageToDisplay.Annotated_Image_Path) {
                // Try different URL formats for annotated images (like in overview.php)
                const imageId = imageToDisplay.Image_ID;
                // Fix path separators for web
                const webPath = imageToDisplay.Annotated_Image_Path.replace(/\\/g, '/');
                console.log(`🔍 Loading annotated image for ${imageId}: ${webPath}`);

                const annotatedUrls = [
                    `../${webPath}`,
                    `../python/${webPath}`,
                    `../static/annotated/${imageId}_annotated.jpg`,
                    `../python/static/annotated/${imageId}_annotated.jpg`
                ];

                showAnnotatedImageWithFallback(imageToDisplay, annotatedUrls, 0);
            } else {
                imageContent.innerHTML = '<div class="loading">Annotated image not available</div>';
            }
        }

        function showDefaultSearchImage() {
            const imageContent = document.getElementById('imageContent');
            imageContent.innerHTML = `
                <img src="../image/searchimage.png"
                     alt="Select a specific image to view or use filters"
                     class="annotated-image">
            `;
        }

        function showAnnotatedImageWithFallback(imageData, urls, index) {
            const imageContent = document.getElementById('imageContent');

            if (index >= urls.length) {
                imageContent.innerHTML = `
                    <div style="text-align: center; color: #64748b;">
                        <div style="font-size: 3rem; margin-bottom: 1rem; color: #dc3545;">❌</div>
                        <div style="font-size: 1rem; font-weight: 500; margin-bottom: 0.5rem;">Annotated image not found</div>
                        <div style="font-size: 0.875rem;">
                            <strong>${imageData.Image_ID}</strong> - ${imageData.Project_Name}<br>
                            Analysis Date: ${new Date(imageData.Analysis_Date).toLocaleDateString()}
                        </div>
                    </div>
                `;
                return;
            }

            const img = new Image();
            img.onload = function() {
                imageContent.innerHTML = `
                    <img src="${urls[index]}"
                         alt="Annotated ${imageData.Image_ID}"
                         class="annotated-image">
                `;
            };

            img.onerror = function() {
                console.log(`Failed to load annotated image with URL ${index + 1}: ${urls[index]}`);
                showAnnotatedImageWithFallback(imageData, urls, index + 1);
            };

            img.src = urls[index];
        }

        function showLoading() {
            const metrics = ["defectCount", "commonDefectType", "commonMaintenanceType", "maintenanceRequired"];
            metrics.forEach(id => {
                document.getElementById(id).innerHTML = '<div class="loading"></div>';
            });
        }

        function showError(message) {
            const metrics = ["defectCount", "commonDefectType", "commonMaintenanceType", "maintenanceRequired"];
            metrics.forEach(id => {
                document.getElementById(id).textContent = 'Error';
            });
        }

        function showBlankMetrics() {
            // Show blank/dash for all metrics when "All Images" is selected
            const metrics = ["defectCount", "commonDefectType", "commonMaintenanceType", "maintenanceRequired"];
            metrics.forEach(id => {
                document.getElementById(id).textContent = '-';
            });

            // Also blank the summary table
            const tableBody = document.getElementById('defectSummaryBody');
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                    </tr>
                `;
            }

            console.log("Showing blank metrics - All Images selected");
        }

        function showBlankCharts() {
            // Clear all charts when "All Images" is selected
            if (charts.defectFrequency) {
                charts.defectFrequency.destroy();
                charts.defectFrequency = null;
            }
            if (charts.defectDistribution) {
                charts.defectDistribution.destroy();
                charts.defectDistribution = null;
            }

            // Show empty state message in chart containers
            const frequencyCanvas = document.getElementById('defectFrequencyChart');
            const distributionCanvas = document.getElementById('defectDistributionChart');

            if (frequencyCanvas) {
                const ctx = frequencyCanvas.getContext('2d');
                ctx.clearRect(0, 0, frequencyCanvas.width, frequencyCanvas.height);
                ctx.fillStyle = '#6b7280';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Select a specific image to view charts', frequencyCanvas.width/2, frequencyCanvas.height/2);
            }

            if (distributionCanvas) {
                const ctx = distributionCanvas.getContext('2d');
                ctx.clearRect(0, 0, distributionCanvas.width, distributionCanvas.height);
                ctx.fillStyle = '#6b7280';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Select a specific image to view charts', distributionCanvas.width/2, distributionCanvas.height/2);
            }

            console.log("Showing blank charts - All Images selected");
        }

        function updateCharts(chartData) {
            updateDefectSummaryTable(chartData.defect_frequency);
            updateDefectFrequencyChart(chartData.defect_frequency);
            updateDefectDistributionChart(chartData.defect_distribution);
        }

        function updateDefectSummaryTable(defectTypesData) {
            const tableBody = document.getElementById('defectSummaryBody');

            if (!defectTypesData || defectTypesData.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                    </tr>
                `;
                return;
            }

            // Sort by frequency (highest count first)
            defectTypesData.sort((a, b) => b.Total_Count - a.Total_Count);

            // Show ALL defect types, not just the most common one
            let tableRows = '';
            defectTypesData.forEach(defect => {
                const maintenanceType = getMaintenanceTypeForDefect(defect.Defect_Type);
                const cleanDefectCount = parseInt(defect.Total_Count) || 0;

                tableRows += `
                    <tr>
                        <td style="color: #8b5cf6; font-weight: 600;">${defect.Defect_Type}</td>
                        <td style="color: #ef4444; font-weight: 600;">${cleanDefectCount}</td>
                        <td style="color: #06b6d4; font-weight: 600;">${maintenanceType}</td>
                    </tr>
                `;
            });

            tableBody.innerHTML = tableRows;
        }

        function updateDefectFrequencyChart(data) {
            const ctx = document.getElementById('defectFrequencyChart').getContext('2d');

            if (charts.defectFrequency) {
                charts.defectFrequency.destroy();
            }

            const labels = data.map(item => item.Defect_Type);
            const counts = data.map(item => item.Total_Count);

            charts.defectFrequency = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Defect Count',
                        data: counts,
                        backgroundColor: [
                             '#2F80ED', '#27AE60', '#F2994A', '#4F4F4F',
                             '#EB5757', '#56CCF2', '#BB6BD9', '#6FCF97'
                        ],
                        borderColor: [
                              '#2F80ED', '#27AE60', '#F2994A', '#4F4F4F',
                              '#EB5757', '#56CCF2', '#BB6BD9', '#6FCF97'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed.y} defects`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45
                            }
                        }
                    }
                }
            });
        }

        function updateDefectDistributionChart(data) {
            const ctx = document.getElementById('defectDistributionChart').getContext('2d');

            if (charts.defectDistribution) {
                charts.defectDistribution.destroy();
            }

            const labels = data.map(item => item.Defect_Type);
            const percentages = data.map(item => item.percentage);

            charts.defectDistribution = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: percentages,
                        backgroundColor: [
                             '#2F80ED', '#27AE60', '#F2994A', '#4F4F4F',
                             '#EB5757', '#56CCF2', '#BB6BD9', '#6FCF97'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                generateLabels: function(chart) {
                                    const data = chart.data;
                                    return data.labels.map((label, index) => ({
                                        text: `${label}: ${data.datasets[0].data[index]}%`,
                                        fillStyle: data.datasets[0].backgroundColor[index],
                                        pointStyle: 'circle'
                                    }));
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // PDF Download functionality
        async function downloadPDF() {
            const downloadBtn = document.getElementById('pdfDownloadBtn');
            const originalText = downloadBtn.innerHTML;

            try {
                // Show loading state
                downloadBtn.disabled = true;
                downloadBtn.innerHTML = '⏳ Generating PDF...';

                // Get current date for filename
                const now = new Date();
                const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
                const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS format

                // Get selected image info for filename
                const selectedImage = document.getElementById('imageFilter').value;
                const imageInfo = selectedImage ? `_${selectedImage}` : '_All_Images';

                const filename = `Solar_Panel_Dashboard${imageInfo}_${dateStr}_${timeStr}.pdf`;

                // Fix for jsPDF global export
                const jsPDF = window.jspdf ? window.jspdf.jsPDF : window.jsPDF;
                if (!jsPDF) {
                    alert('jsPDF library not loaded.');
                    downloadBtn.disabled = false;
                    downloadBtn.innerHTML = originalText;
                    return;
                }
                const pdf = new jsPDF('p', 'mm', 'a4');

                // Add title
                pdf.setFontSize(20);
                pdf.setTextColor(74, 144, 226);
                pdf.text('Solar Panel Defect Analysis Dashboard', 20, 20);

                // Add date
                pdf.setFontSize(12);
                pdf.setTextColor(108, 117, 125);
                pdf.text(`Generated on: ${now.toLocaleDateString('en-US', {
                    weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'
                })} at ${now.toLocaleTimeString()}`, 20, 30);

                // Add selected filters info
                const projectSearch = document.getElementById('projectSearch').value;
                const dateFilter = document.getElementById('dateFilter').value;
                let filtersText = 'Filters: ';
                if (selectedImage) filtersText += `Image: ${selectedImage}, `;
                if (projectSearch) filtersText += `Project: ${projectSearch}, `;
                if (dateFilter) filtersText += `Date: ${dateFilter}, `;
                if (filtersText === 'Filters: ') filtersText += 'None';
                else filtersText = filtersText.slice(0, -2); // Remove last comma

                pdf.text(filtersText, 20, 40);

                let yPosition = 50;

                // Capture the main dashboard content (metrics, image, table, charts) as a single image
                const dashboardMain = document.querySelector('.dashboard-grid');
                if (dashboardMain) {
                    const dashboardCanvas = await html2canvas(dashboardMain, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#fff'
                    });

                    const dashboardImgData = dashboardCanvas.toDataURL('image/png');
                    const dashboardWidth = 170;
                    const dashboardHeight = (dashboardCanvas.height * dashboardWidth) / dashboardCanvas.width;

                    pdf.addImage(dashboardImgData, 'PNG', 20, yPosition, dashboardWidth, dashboardHeight);
                    yPosition += dashboardHeight + 10;
                }

                // Add footer
                const pageCount = pdf.internal.getNumberOfPages();
                for (let i = 1; i <= pageCount; i++) {
                    pdf.setPage(i);
                    pdf.setFontSize(10);
                    pdf.setTextColor(108, 117, 125);
                    pdf.text(`Page ${i} of ${pageCount} - Solar Panel Analysis Report`, 20, 290);
                }

                // Save the PDF
                pdf.save(filename);

                console.log('✅ PDF downloaded successfully:', filename);

            } catch (error) {
                console.error('❌ Error generating PDF:', error);
                alert('Error generating PDF. Please try again.');
            } finally {
                // Restore button state
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = originalText;
            }
        }
    </script>

    <?php include '../PHP/Dronlytics Footer V2.php'; ?>
</body>
</html>



