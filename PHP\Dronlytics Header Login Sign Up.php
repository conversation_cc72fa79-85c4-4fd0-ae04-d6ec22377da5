<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dronlytics Sign Up</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Header Login Sign Up.css">
</head>
<body>

  <!-- === HEADER FOR LOGIN AND SIGN UP === -->
  <header>
    <div class="header-container">
      <div class="logo">
        <img src="https://i.imgur.com/gZXDmtz.png" alt="Logo" class="logo-image">
        <div class="logo-text">DRONLYTICS</div>
      </div>

      <!-- Navigation for larger screens -->
      <nav>
        <div class="nav-links">
          <a href="../PHP/Dronlytics Landing Page.php">Home</a>
          <a href="../PHP/Dronlytics About Us.php">About Us</a>
        </div>
      </nav>
    </div>
  </header>

  <!-- === HAMBURGER MENU TOGGLE === -->
  <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">☰</div>

  <!-- === MOBILE SIDEBAR === -->
  <div class="mobile-sidebar" id="mobileSidebar">
    <a href="../PHP/Dronlytics Landing Page.php">Home</a>
    <a href="../PHP/Dronlytics About Us.php">About Us</a>
  </div>

  <!-- === OVERLAY === -->
  <div class="mobile-overlay" id="mobileOverlay" onclick="toggleMobileMenu()"></div>

  <!-- === JAVASCRIPT === -->
  <script>
    function toggleMobileMenu() {
      const sidebar = document.getElementById('mobileSidebar');
      const overlay = document.getElementById('mobileOverlay');
      sidebar.classList.toggle('open');
      overlay.classList.toggle('show');
    }
  </script>

</body>
</html>