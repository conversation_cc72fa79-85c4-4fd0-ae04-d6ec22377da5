@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', sans-serif;
  background-color: #f6f9fc;
  height: 100vh;
  overflow: hidden;
}

.container {
  max-width: 1400px;
  margin: auto;
  padding: 15px;
  height: calc(100vh - 80px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

h1 {
  text-align: center;
  font-size: 1.5rem;
  color: #1e293b;
  margin: 0;
}

.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.date {
  font-size: 0.9rem;
  color: #6b7280;
}

.filters-section {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.filters-grid {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

select, input[type="date"], input[type="text"] {
  padding: 0.4rem 0.6rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  background: white;
  min-width: 100px;
  height: 32px;
}

select:focus, input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1rem;
}

.apply-btn {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  height: 32px;
}

.apply-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.pdf-download-btn {
  background: #10b981;
  color: white;
  border: 1px solid #10b981;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  height: 32px;
}

.pdf-download-btn:hover {
  background: #059669;
  border-color: #059669;
}

.pdf-download-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
  margin-top: 0.75rem;
  flex-shrink: 0;
}

.metric-card {
  text-align: center;
  padding: 1.2rem;
  background: white;
  border-radius: 10px;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.metric-card h3 {
  font-size: 1.2rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.4rem;
  line-height: 1.2;
}

.metric-value {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
  line-height: 1.1;
}

/* Smaller text for specific metric cards */
.metric-card.common-defect .metric-value,
.metric-card.maintenance .metric-value,
.metric-card.common-maintenance .metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
}

.metric-card.total .metric-value { color: #3b82f6; }
.metric-card.healthy .metric-value { color: #10b981; }
.metric-card.defect .metric-value { color: #ef4444; }
.metric-card.rate .metric-value { color: #f59e0b; }
.metric-card.common-defect .metric-value { color: #8b5cf6; }
.metric-card.maintenance .metric-value { color: #f59e0b; }
.metric-card.common-maintenance .metric-value { color: #06b6d4; }

.metric-change {
  font-size: 0.7rem;
  color: #6b7280;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}

.images-section {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  border: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.images-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.image-container {
  background: #f8fafc;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: none;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.image-container h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  text-align: center;
}

.image-preview {
  width: 250px;
  height: 250px;
  background: white;
  border: 2px solid #22d3ee;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 0.9rem;
  transition: opacity 0.3s ease;
  position: relative;
  object-fit: contain;
  object-position: center;
  margin: 0 auto;
}

/* Default placeholder image - no stretching */
.image-preview.placeholder {
  object-fit: contain;
}

/* Specific uploaded images - fit the box */
.image-preview.loaded {
  object-fit: cover;
}

.image-preview.loading {
  opacity: 0.6;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.image-preview.loaded {
  opacity: 1;
}

.image-preview.error {
  opacity: 0.5;
  background: #fee;
}

/* Image placeholder with magnifying glass icon */
.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.image-icon {
  width: 50px;
  height: 50px;
  border: 2px solid #9ca3af;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  position: relative;
  background: white;
}

.image-icon::before {
  content: '';
  width: 18px;
  height: 14px;
  background: #9ca3af;
  border-radius: 3px 3px 0 0;
  position: relative;
}

.image-icon::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #9ca3af;
  border-radius: 50%;
  top: 10px;
  right: 10px;
}

.magnifier {
  position: absolute;
  bottom: -3px;
  right: -3px;
  width: 18px;
  height: 18px;
  border: 2px solid #9ca3af;
  border-radius: 50%;
  background: white;
}

.magnifier::after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: -6px;
  width: 6px;
  height: 2px;
  background: #9ca3af;
  transform: rotate(45deg);
  border-radius: 1px;
}

.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .container { padding: 10px; }
  h1 { font-size: 1.3rem; }
  .filters-section { flex-direction: column; align-items: stretch; }
  .filters-grid { flex-direction: column; }
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  .metric-card {
    min-height: 60px;
    padding: 0.3rem;
  }
  .metric-card h3 { font-size: 0.65rem; }
  .metric-value { font-size: 1.2rem; }
  .metric-card.common-defect .metric-value,
  .metric-card.maintenance .metric-value,
  .metric-card.common-maintenance .metric-value {
    font-size: 0.8rem;
  }
  .images-grid { grid-template-columns: 1fr; }
  .image-preview {
    width: 200px;
    height: 200px;
  }
}

/* ===== Mobile View (768px and below) ===== */
@media (max-width: 768px) {
  /* Layout + scroll */
  body { padding-top: 80px; overflow: auto; height: auto; }
  .container { padding: 15px; height: auto; overflow: visible; }

  /* Header */
  .header-bar {
    display: flex; flex-direction: column; align-items: flex-start;
    gap: 6px; margin-bottom: 18px;
  }
  h1 { font-size: 1.5rem; text-align: left; color: #1e293b; margin: 0; font-weight: 600; }
  .date { font-size: 0.9rem; color: #6b7280; }
  .date small { display: block; font-size: 0.7rem; color: #9ca3af; }

  /* Filters block — centered column like the mock */
  .filters-section {
    display: flex; flex-direction: column; align-items: center;
    gap: 16px; margin-bottom: 20px;
  }

  /* A fixed comfortable control width to match the screenshot */
  :root { --ctrl-w: 240px; }

  .filter-group {
    display: flex; flex-direction: column; gap: 6px; width: var(--ctrl-w);
  }

  .filter-group label {
    font-size: 0.95rem; font-weight: 600; color: #374151; text-align: left;
  }

  /* Inputs / selects */
  select,
  input[type="date"],
  input[type="text"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    background: #fff;
  }

  /* Optional: search icon inside the project input */
  .search-wrapper { position: relative; width: var(--ctrl-w); }
  .search-wrapper input { padding-right: 36px; }
  .search-wrapper .search-icon {
    position: absolute; right: 10px; top: 50%; transform: translateY(-50%);
    font-size: 18px; opacity: .7; pointer-events: none;
  }

  /* Buttons */
  .reset-btn,
  .pdf-download-btn,
  .apply-btn { /* keep .apply-btn for compatibility */
    width: var(--ctrl-w);
    padding: 10px 12px;
    margin: 6px auto 0;
    text-align: center;
    border-radius: 10px;
    border: none;
    font-weight: 700;
    font-size: 0.95rem;
    display: block;
  }

  .reset-btn,
  .apply-btn { background: #3b82f6; color: #fff; }        /* blue */
  .pdf-download-btn { background: #10b981; color: #fff; } /* green */

  /* Metrics grid (2 columns -> 1 later) */
  .metrics-grid {
    display: grid; grid-template-columns: repeat(2, 1fr);
    gap: 10px; margin: 20px 0;
  }
  .metric-card { padding: 12px; min-height: 80px; }

  /* Images section */
  .images-grid { display: grid; grid-template-columns: 1fr; gap: 15px; }
  .image-container { padding: 12px; }
  .image-preview { width: 100%; max-width: 300px; height: 200px; margin: 0 auto; }
}

/* ===== Smaller Phones (480px and below) ===== */
@media (max-width: 480px) {
  :root { --ctrl-w: 220px; }  /* a bit narrower on tiny screens */

  .metrics-grid { grid-template-columns: 1fr; }
  .metric-card { min-height: 70px; padding: 10px; }
  h1 { font-size: 1.3rem; }
}

/* ===== Mobile View (768px and below) ===== */
@media (max-width: 768px) {
  /* ... your original code here ... */

  /* Override select/input to fully show IMGxxx text */
  select,
  input[type="date"],
  input[type="text"] {
    width: 100%;
    padding: 8px 10px;         
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    background: #fff;
    white-space: nowrap;       
    text-overflow: ellipsis;   
  }

  select {
    appearance: none;          
    background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='16' viewBox='0 0 24 24' width='16' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 8px center; 
    background-size: 16px;
    padding-right: 28px;       
  }
}

/* ===== Smaller Phones (480px and below) ===== */
@media (max-width: 480px) {
  /* ... your original small-phone code here ... */
}
