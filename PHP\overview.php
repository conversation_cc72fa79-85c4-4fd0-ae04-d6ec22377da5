<?php
// overview.php — Solar Panel Defect Detection Overview Dashboard
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../PHP/Dronlytics Login Page.php");
    exit();
}

$user_id = $_SESSION['user_id']; // Get from session

// Handle API requests for defect types data
if (isset($_GET['api']) && $_GET['api'] === 'defect_types') {
    header("Content-Type: application/json");

    $mysqli = new mysqli("localhost", "root", "", "userdb");

    if ($mysqli->connect_error) {
        http_response_code(500);
        echo json_encode(["error" => "DB connection failed"]);
        exit();
    }

    // Function to calculate maintenance type
    function calculateMaintenanceType($defectType) {
        $defectLower = strtolower(str_replace('_', '-', $defectType));

        if (strpos($defectLower, 'clean') !== false) {
            return 'None';
        } elseif (strpos($defectLower, 'bird-drop') !== false || strpos($defectLower, 'dusty') !== false) {
            return 'Clean';
        } elseif (strpos($defectLower, 'electrical-damage') !== false) {
            return 'Repair';
        } elseif (strpos($defectLower, 'crack') !== false) {
            return 'Replace';
        }
        // Removed physical-damage and default fallback as requested
        return null;
    }

    // Get defect types data
    $project = $_GET['project'] ?? null;
    $date = $_GET['date'] ?? null;
    $image_id = $_GET['image_id'] ?? null;

    // Check if Maintenance_Type column exists
    $columnCheck = $mysqli->query("SHOW COLUMNS FROM image_defects LIKE 'Maintenance_Type'");
    $hasMaintenanceColumn = $columnCheck && $columnCheck->num_rows > 0;

    if ($hasMaintenanceColumn) {
        $defectTypesQuery = "
            SELECT
                id.Defect_Type,
                id.Maintenance_Type,
                SUM(id.Defect_Count) as Total_Count
            FROM image_defects id
            INNER JOIN image_uploaded iu ON id.Image_ID = iu.Image_ID
            WHERE iu.User_ID = ?
        ";
    } else {
        $defectTypesQuery = "
            SELECT
                id.Defect_Type,
                'Inspect' as Maintenance_Type,
                SUM(id.Defect_Count) as Total_Count
            FROM image_defects id
            INNER JOIN image_uploaded iu ON id.Image_ID = iu.Image_ID
            WHERE iu.User_ID = ?
        ";
    }
    $defectParams = [$user_id];

    if ($project) {
        $defectTypesQuery .= " AND iu.Project_Name = ?";
        $defectParams[] = $project;
    }

    if ($date) {
        $defectTypesQuery .= " AND DATE(iu.Upload_Date) = ?";
        $defectParams[] = $date;
    }

    if ($image_id) {
        $defectTypesQuery .= " AND iu.Image_ID = ?";
        $defectParams[] = $image_id;
    }

    $defectTypesQuery .= " GROUP BY id.Defect_Type, id.Maintenance_Type ORDER BY Total_Count DESC";

    $defectStmt = $mysqli->prepare($defectTypesQuery);
    $defectStmt->bind_param(str_repeat("s", count($defectParams)), ...$defectParams);
    $defectStmt->execute();
    $defectResult = $defectStmt->get_result();

    $defectTypes = [];
    $maintenanceTypes = [];
    $totalDefects = 0;

    while ($row = $defectResult->fetch_assoc()) {
        // Calculate maintenance type if not in database
        if (!$hasMaintenanceColumn || empty($row['Maintenance_Type'])) {
            $row['Maintenance_Type'] = calculateMaintenanceType($row['Defect_Type']);
        }

        $defectTypes[] = $row;
        $totalDefects += $row['Total_Count'];

        // Count maintenance types
        $maintenanceType = $row['Maintenance_Type'];
        if (!isset($maintenanceTypes[$maintenanceType])) {
            $maintenanceTypes[$maintenanceType] = 0;
        }
        $maintenanceTypes[$maintenanceType] += $row['Total_Count'];
    }

    // Calculate percentages for defect types
    foreach ($defectTypes as &$defect) {
        $defect['percentage'] = $totalDefects > 0 ? round(($defect['Total_Count'] / $totalDefects) * 100, 1) : 0;
    }

    // Find most common maintenance type
    $commonMaintenanceType = "-";
    if (!empty($maintenanceTypes)) {
        arsort($maintenanceTypes);
        $commonMaintenanceType = array_key_first($maintenanceTypes);
    }

    $response = [
        'defect_types' => $defectTypes,
        'maintenance_types' => $maintenanceTypes,
        'common_maintenance_type' => $commonMaintenanceType,
        'total_defects' => $totalDefects
    ];

    echo json_encode($response);
    $mysqli->close();
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Solar Panel Defect Detection Overview - Dronlytics</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Header After Login.css">
  <link rel="stylesheet" href="../CSS/overview.css">
  <link rel="stylesheet" href="../CSS/Dronlytics Footer V2.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

</head>
<body style="padding-top: 80px;">

  <?php include '../PHP/Dronlytics Header After Login.php'; ?>

  <!-- Arrow to dashboard -->
  <div style="position: fixed; top: 50%; right: 30px; transform: translateY(-50%); z-index: 1000;">
    <button id="toDashboardBtn" title="Go to Dashboard" style="background: #4a90e2; color: #fff; border: none; border-radius: 50%; width: 48px; height: 48px; font-size: 2rem; box-shadow: 0 2px 8px rgba(0,0,0,0.12); cursor: pointer; display: flex; align-items: center; justify-content: center; transition: background 0.2s;">
      &rarr;
    </button>
  </div>

  <div class="container">
    <div class="header-bar">
      <h1>Solar Panel Defect Detection Overview</h1>
      <div class="date"><span id="currentDate">12 July 2025</span><br><small>TODAY'S DATE</small></div>
    </div>
      <!-- Filters Section -->
      <div class="filters-section">
        <div class="filters-grid">
          <div class="filter-group">
            <label for="imageFilter">Image Selection</label>
            <select id="imageFilter">
              <option value="">All Images</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="projectSearch">Project Name Search</label>
            <div class="search-container">
              <input type="text" id="projectSearch" placeholder="Search Project Name...">
              <span class="search-icon">🔍</span>
            </div>
          </div>

          <div class="filter-group">
            <label for="dateFilter">Date Filter</label>
            <input type="date" id="dateFilter">
          </div>

          <div class="filter-group">
            <label>&nbsp;</label>
           <button class="apply-btn" onclick="refreshDashboard(event)">🔄 Reset Data</button>
          </div>

          <div class="filter-group">
            <label>&nbsp;</label>
            <button class="pdf-download-btn" onclick="downloadPDF()" id="pdfDownloadBtn">
              📄 Download PDF
            </button>
          </div>
        </div>
      </div>

      <!-- Images Section -->
      <div class="images-section">
        <div id="imageStatus" style="text-align: center; margin-bottom: 1rem; font-style: italic; color: #6b7280;"></div>
        <div class="images-grid">
          <div class="image-container">
            <h4 id="originalImageTitle">Original Image</h4>
            <img id="originalImage" class="image-preview" src="../image/searchimage.png" alt="Original drone image">
          </div>
          <div class="image-container">
            <h4 id="annotatedImageTitle">Processed Image</h4>
            <img id="annotatedImage" class="image-preview" src="../image/searchimage.png" alt="Annotated Drone Image">
          </div>
        </div>
      </div>

      <!-- Metrics Section -->
      <div class="metrics-grid">
        <div class="metric-card common-defect">
          <h3>Common Defect Type</h3>
          <div class="metric-value" id="commonDefectType">-</div>
        </div>
        <div class="metric-card defect">
          <h3>Defect Count</h3>
          <div class="metric-value" id="defectCount">-</div>
        </div>
        <div class="metric-card maintenance">
          <h3>Maintenance Required</h3>
          <div class="metric-value" id="maintenanceRequired">-</div>
        </div>
        <div class="metric-card common-maintenance">
          <h3>Common Maintenance Type</h3>
          <div class="metric-value" id="commonMaintenanceType">-</div>
        </div>
      </div>
    </div>
  </div>

  <!-- === JAVASCRIPT === -->
  <script>
    // Arrow button: Go to dashboard for current image
    document.addEventListener('DOMContentLoaded', function() {
      const btn = document.getElementById('toDashboardBtn');
      btn.addEventListener('click', function() {
        // Get selected image ID
        const imageId = document.getElementById('imageFilter').value;
        let url = '../PHP/dashboard_mysql.php';
        if (imageId) {
          url += '?image_id=' + encodeURIComponent(imageId);
        }
        window.open(url, '_blank');
      });
    });
    let allData = [];

    // Get URL parameters
    function getUrlParameter(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }

    async function loadDashboard(preserveSelection = false) {
      try {
        // Show loading state
        showLoading();

        // Get filter values
        let selectedImage = document.getElementById("imageFilter").value;
        const projectSearch = document.getElementById("projectSearch").value.toLowerCase().trim();
        const dateFilter = document.getElementById("dateFilter").value;

        // Check for URL parameter on first load
        const urlImageId = getUrlParameter('image_id');
        if (urlImageId && !preserveSelection) {
            selectedImage = urlImageId;
            console.log('🔍 Using URL image_id parameter:', urlImageId);
        }

        console.log("Loading dashboard with filters:", { selectedImage, projectSearch, dateFilter });

        // Fetch data from server
        const url = new URL("../PHP/get_overview_data.php", window.location.href);
        url.searchParams.set("user", "<?php echo $user_id; ?>");

        const res = await fetch(url);
        const data = await res.json();

        if (data.error) {
          console.error("Error:", data.error);
          hideLoading();
          return;
        }

        allData = data;
        console.log("Fetched data:", data);

        // Populate dropdowns only on initial load
        if (!preserveSelection) {
          populateImageDropdown(data);

          // Check for image_id parameter from URL and auto-select it
          const urlImageId = getUrlParameter('image_id');
          if (urlImageId) {
            console.log('🔍 Auto-selecting image from URL parameter:', urlImageId);
            const imageSelect = document.getElementById("imageFilter");
            imageSelect.value = urlImageId;
            // Update selectedImage variable to match URL parameter
            selectedImage = urlImageId;
            console.log('✅ Updated selectedImage variable to:', selectedImage);
          }
        }

        // Apply filters step by step
        let filteredData = [...data]; // Create a copy

        // Filter by project search first
        if (projectSearch) {
          filteredData = filteredData.filter(row =>
            row.Project_Name && row.Project_Name.toLowerCase().includes(projectSearch)
          );
          console.log("After project filter:", filteredData);
        }

        // Filter by date
        if (dateFilter) {
          filteredData = filteredData.filter(row => row.Analysis_Date === dateFilter);
          console.log("After date filter:", filteredData);
        }

        // Filter by selected image 
        if (selectedImage) {
          filteredData = filteredData.filter(row => row.Image_ID === selectedImage);
          console.log("After image filter:", filteredData);
        }

        console.log("Final filtered data:", filteredData);

        // Update metrics
        updateMetrics(filteredData);

        // Determine which image to show
        const imageToShow = determineImageToShow(selectedImage, projectSearch, dateFilter, filteredData);

        // Update images
        updateImages(imageToShow, filteredData);

      } catch (error) {
        console.error("Error loading dashboard:", error);
        hideLoading();
      }
    }

    function determineImageToShow(selectedImage, projectSearch, dateFilter, filteredData) {
      // Priority 1: If user explicitly selected an image, use that
      if (selectedImage && selectedImage !== "") {
        console.log("Using explicitly selected image:", selectedImage);
        // Clear status message for manual selection
        document.getElementById("imageStatus").textContent = "";
        return selectedImage;
      }

      // Priority 2: If filtering by project or date, show first available image
      if ((projectSearch || dateFilter) && filteredData.length > 0) {
        // Sort by Image_ID to get consistent "first" image
        const sortedData = filteredData.sort((a, b) => a.Image_ID.localeCompare(b.Image_ID));
        const firstImage = sortedData[0].Image_ID;

        console.log(`Auto-selecting first image for ${projectSearch ? 'project' : 'date'} filter:`, firstImage);

        // Update the dropdown to reflect this selection
        const imageSelect = document.getElementById("imageFilter");
        imageSelect.value = firstImage;

        // Show status message
        const imageStatus = document.getElementById("imageStatus");
        if (projectSearch) {
          imageStatus.textContent = `📸 Showing first image from project: "${projectSearch}"`;
        } else if (dateFilter) {
          imageStatus.textContent = `📅 Showing first image from date: ${dateFilter}`;
        }

        return firstImage;
      }

      // Priority 3: No specific image (show default search images)
      console.log("No specific image to show - using default");
      // Clear status message
      document.getElementById("imageStatus").textContent = "";
      return null;
    }

    function populateImageDropdown(data) {
      const imageSelect = document.getElementById("imageFilter");
      const currentValue = imageSelect.value; // Preserve current selection

      // Clear existing options except "All Images"
      while (imageSelect.options.length > 1) {
        imageSelect.removeChild(imageSelect.lastChild);
      }

      // Get unique images from data that have results
      const images = [...new Set(data.map(row => row.Image_ID))].filter(Boolean).sort();

      console.log("Populating dropdown with images:", images);

      images.forEach(imageId => {
        const option = document.createElement("option");
        option.value = imageId;
        option.textContent = imageId;
        imageSelect.appendChild(option);
      });

      // Restore selection if it still exists
      if (currentValue && images.includes(currentValue)) {
        imageSelect.value = currentValue;
      } else if (currentValue && !images.includes(currentValue)) {
        // If selected image is no longer available, reset to "All Images"
        imageSelect.value = "";
      }
    }

    function updateMetrics(data) {
      console.log("Updating metrics with data:", data);

      // Check if "All Images" is selected (no specific image)
      const selectedImage = document.getElementById("imageFilter").value;

      if (!selectedImage || selectedImage === '') {
        // Show blank metrics when "All Images" is selected
        showBlankMetrics();
        hideLoading();
        return;
      }

      // Only process rows that have valid data
      const validData = data.filter(row =>
        row.Total_Panel_Count !== null &&
        row.Total_Panel_Count !== undefined &&
        row.Total_Panel_Count > 0
      );

      console.log("Valid data for metrics:", validData);

      // Calculate defect count (total defects across all images)
      let totalDefects = 0;
      validData.forEach(row => {
        totalDefects += parseInt(row.Defect_Panel_Count) || 0;
      });

      // Calculate maintenance required (same logic as dashboard)
      let maintenanceRequired = "No";
      if (totalDefects > 0) {
        maintenanceRequired = "Yes";
      }

      // Get defect types data to find common defect type
      fetchDefectTypesData(validData).then(apiResponse => {
        let commonDefectType = "-";
        let commonMaintenanceType = "-";

        if (apiResponse && apiResponse.defect_types && apiResponse.defect_types.length > 0) {
          // Sort by frequency to get the most common defect
          apiResponse.defect_types.sort((a, b) => b.Total_Count - a.Total_Count);
          commonDefectType = apiResponse.defect_types[0].Defect_Type;

          // Use maintenance type from API response
          commonMaintenanceType = apiResponse.common_maintenance_type || apiResponse.defect_types[0].Maintenance_Type || "-";
        }

        // Update metric displays
        document.getElementById("defectCount").textContent = totalDefects.toLocaleString();
        document.getElementById("commonDefectType").textContent = commonDefectType;
        document.getElementById("commonMaintenanceType").textContent = commonMaintenanceType;
        document.getElementById("maintenanceRequired").textContent = maintenanceRequired;

        console.log("Updated metrics:", {
          defectCount: totalDefects,
          commonDefectType,
          commonMaintenanceType,
          maintenanceRequired
        });

        hideLoading();
      });
    }

    function getMaintenanceTypeForDefect(defectType) {
      if (!defectType || defectType === "-") return "-";

      const defectLower = defectType.toLowerCase().replace('_', '-');

      if (defectLower.includes('clean')) {
        return 'None';
      } else if (defectLower.includes('bird-drop') || defectLower.includes('dusty')) {
        return 'Clean';
      } else if (defectLower.includes('electrical-damage')) {
        return 'Repair';
      } else if (defectLower.includes('physical-damage')) {
        return 'Seal'; // or 'Replace' for large areas
      } else {
        return 'Inspect';
      }
    }

    async function fetchDefectTypesData(validData) {
      try {
        // Get current filter values
        const selectedImage = document.getElementById("imageFilter").value;
        const projectSearch = document.getElementById("projectSearch").value.trim();
        const dateFilter = document.getElementById("dateFilter").value;

        // Build query parameters
        const params = new URLSearchParams({
          api: 'defect_types'
        });

        if (selectedImage && selectedImage !== '') {
          params.append('image_id', selectedImage);
        }
        if (projectSearch) {
          params.append('project', projectSearch);
        }
        if (dateFilter) {
          params.append('date', dateFilter);
        }

        const response = await fetch(`overview.php?${params.toString()}`);
        const data = await response.json();

        return data;
      } catch (error) {
        console.error("Error fetching defect types:", error);
        return { defect_types: [], common_maintenance_type: "-" };
      }
    }

    function updateImages(imageToShow, filteredData) {
      const originalImg = document.getElementById("originalImage");
      const annotatedImg = document.getElementById("annotatedImage");

      console.log("Updating images - imageToShow:", imageToShow, "filteredData length:", filteredData.length);

      // Reset images first
      originalImg.onerror = null;
      originalImg.onload = null;

      if (imageToShow && imageToShow !== "" && filteredData.length > 0) {
        // Show specific image
        const imageData = filteredData.find(row => row.Image_ID === imageToShow);

        if (!imageData) {
          console.error("Image data not found for:", imageToShow);
          showDefaultImages();
          return;
        }

        console.log("Selected image data:", imageData);

        // Extract Google Drive file ID from Image_Path
        const imagePath = imageData.Image_Path || "";
        let fileId = "";

        if (imagePath.includes("id=")) {
          fileId = imagePath.split("id=")[1].split("&")[0];
        } else if (imagePath.includes("/d/")) {
          fileId = imagePath.split("/d/")[1].split("/")[0];
        }

        console.log("Extracted file ID:", fileId);

        if (fileId) {
          // Optimized URLs for faster loading (proxy first for caching)
          const driveUrls = [
            `../PHP/image_proxy.php?id=${fileId}&size=medium`, // Cached proxy first
            `https://drive.google.com/thumbnail?id=${fileId}&sz=w800`, // Optimized size
            `https://lh3.googleusercontent.com/d/${fileId}=w800`,
            `https://drive.google.com/uc?export=download&id=${fileId}` // Fallback
          ];

          // Function to try different URL formats with loading states
          function tryLoadImage(urls, index = 0) {
            if (index >= urls.length) {
              originalImg.className = "image-preview error";
              originalImg.src = "../image/searchimage.png";
              originalImg.alt = "Image failed to load - check Google Drive permissions";
              console.error("All image URL formats failed for file ID:", fileId);
              return;
            }

            // Show loading state
            originalImg.className = "image-preview loading";

            // Create new image object for preloading
            const tempImg = new Image();
            tempImg.onload = function() {
              // Image loaded successfully, now display it
              originalImg.src = tempImg.src;
              originalImg.className = "image-preview loaded";
              originalImg.alt = `Original image ${imageToShow} (${imageData.Project_Name || 'Unknown Project'})`;

              console.log(`✅ Successfully loaded image ${imageToShow} with URL format ${index + 1}`);

              // Update image titles with context
              document.getElementById("originalImageTitle").textContent =
                `Original Image - ${imageToShow} (${imageData.Project_Name || 'Unknown Project'})`;
              document.getElementById("annotatedImageTitle").textContent =
                `Processed Image - ${imageToShow}`;
            };

            tempImg.onerror = function() {
              console.log(`Failed to load image with URL format ${index + 1}: ${urls[index]}`);
              tryLoadImage(urls, index + 1);
            };

            // Start loading
            tempImg.src = urls[index];
          }

          tryLoadImage(driveUrls);

          // Handle annotated image
          loadAnnotatedImage(imageData, imageToShow);
        } else {
          // No valid file ID found
          console.error("No valid file ID found in path:", imagePath);
          showDefaultImages();
        }
      } else {
        // Show default search images for "All Images" or no selection
        showDefaultImages();
      }
    }

    function loadAnnotatedImage(imageData, imageToShow) {
      const annotatedImg = document.getElementById("annotatedImage");

      // Check if annotated image path exists
      const annotatedPath = imageData.Annotated_Image_Path;

      if (annotatedPath && annotatedPath !== null && annotatedPath !== "") {
        console.log("Loading annotated image:", annotatedPath);

        // Handle different types of annotated image paths
        if (annotatedPath.startsWith("https://drive.google.com")) {
          // Google Drive URL - extract file ID and try different formats
          let fileId = "";
          if (annotatedPath.includes("id=")) {
            fileId = annotatedPath.split("id=")[1].split("&")[0];
          } else if (annotatedPath.includes("/d/")) {
            fileId = annotatedPath.split("/d/")[1].split("/")[0];
          }

          if (fileId) {
            const annotatedUrls = [
              `../PHP/image_proxy.php?id=${fileId}&size=medium`,
              `https://drive.google.com/thumbnail?id=${fileId}&sz=w800`,
              `https://lh3.googleusercontent.com/d/${fileId}=w800`,
              `https://drive.google.com/uc?export=download&id=${fileId}`
            ];

            tryLoadAnnotatedImage(annotatedUrls, 0, imageToShow);
          } else {
            // Fallback to placeholder
            showAnnotatedPlaceholder(imageToShow);
          }
        } else if (annotatedPath.startsWith("static/")) {
          // Local file path - add cache busting and loading state
          annotatedImg.className = "image-preview loading";

          // Fix path separators for web
          const webPath = annotatedPath.replace(/\\/g, '/');
          console.log(`🔍 Loading local annotated image: ${webPath}`);

          const tempImg = new Image();
          tempImg.onload = function() {
            annotatedImg.src = tempImg.src;
            annotatedImg.className = "image-preview loaded";
            annotatedImg.alt = `Annotated image ${imageToShow}`;
            console.log("✅ Successfully loaded local annotated image");
          };
          tempImg.onerror = function() {
            console.log("❌ Failed to load local annotated image");
            showAnnotatedPlaceholder(imageToShow);
          };

          // Try multiple possible paths for local annotated images
          const possiblePaths = [
            `../${webPath}`,
            `../python/${webPath}`,
            `../static/annotated/${imageToShow}_annotated.jpg`,
            `../python/static/annotated/${imageToShow}_annotated.jpg`
          ];

          function tryLoadLocalImage(paths, index) {
            if (index >= paths.length) {
              console.log("❌ Failed to load local annotated image from all paths");
              showAnnotatedPlaceholder(imageToShow);
              return;
            }

            const testImg = new Image();
            testImg.onload = function() {
              annotatedImg.src = testImg.src;
              annotatedImg.className = "image-preview loaded";
              annotatedImg.alt = `Annotated image ${imageToShow}`;
              console.log(`✅ Successfully loaded local annotated image from: ${paths[index]}`);
            };
            testImg.onerror = function() {
              console.log(`❌ Failed to load from: ${paths[index]}`);
              tryLoadLocalImage(paths, index + 1);
            };
            testImg.src = `${paths[index]}?t=${Date.now()}`;
          }

          tryLoadLocalImage(possiblePaths, 0);
        } else {
          // Unknown format, use as-is with loading state
          annotatedImg.className = "image-preview loading";

          const tempImg = new Image();
          tempImg.onload = function() {
            annotatedImg.src = tempImg.src;
            annotatedImg.className = "image-preview loaded";
            annotatedImg.alt = `Annotated image ${imageToShow}`;
          };
          tempImg.onerror = function() {
            showAnnotatedPlaceholder(imageToShow);
          };
          tempImg.src = annotatedPath;
        }
      } else {
        // No annotated image available
        console.log("No annotated image available for", imageToShow);
        showAnnotatedPlaceholder(imageToShow, "No annotated image available");
      }
    }

    function tryLoadAnnotatedImage(urls, index, imageToShow) {
      const annotatedImg = document.getElementById("annotatedImage");

      if (index >= urls.length) {
        showAnnotatedPlaceholder(imageToShow, "Failed to load annotated image");
        return;
      }

      // Show loading state
      annotatedImg.className = "image-preview loading";

      // Create new image object for preloading
      const tempImg = new Image();
      tempImg.onload = function() {
        annotatedImg.src = tempImg.src;
        annotatedImg.className = "image-preview loaded";
        annotatedImg.alt = `Annotated image ${imageToShow}`;
        console.log(`✅ Successfully loaded annotated image with URL format ${index + 1}`);
      };

      tempImg.onerror = function() {
        console.log(`Failed to load annotated image with URL format ${index + 1}: ${urls[index]}`);
        tryLoadAnnotatedImage(urls, index + 1, imageToShow);
      };

      tempImg.src = urls[index];
    }

    function showAnnotatedPlaceholder(imageToShow, message = "Annotated image processing...") {
      const annotatedImg = document.getElementById("annotatedImage");
      annotatedImg.src = "../image/searchimage.png";
      annotatedImg.alt = message;
      annotatedImg.className = "image-preview placeholder";
      console.log(`Showing placeholder for ${imageToShow}: ${message}`);
    }

    function showDefaultImages() {
      const originalImg = document.getElementById("originalImage");
      const annotatedImg = document.getElementById("annotatedImage");

      console.log("Showing default search images");
      originalImg.src = "../image/searchimage.png";
      originalImg.alt = "Select a specific image to view or use filters";
      originalImg.className = "image-preview placeholder";

      annotatedImg.src = "../image/searchimage.png";
      annotatedImg.alt = "Select a specific image to view or use filters";
      annotatedImg.className = "image-preview placeholder";

      // Reset image titles
      document.getElementById("originalImageTitle").textContent = "Original Drone Image";
      document.getElementById("annotatedImageTitle").textContent = "Annotated Drone Image";
    }

    function showLoading() {
      const metrics = ["defectCount", "commonDefectType", "commonMaintenanceType", "maintenanceRequired"];
      metrics.forEach(id => {
        document.getElementById(id).innerHTML = '<div class="loading"></div>';
      });
    }

    function showBlankMetrics() {
      // Show blank/dash for all metrics when "All Images" is selected
      const metrics = ["defectCount", "commonDefectType", "commonMaintenanceType", "maintenanceRequired"];
      metrics.forEach(id => {
        document.getElementById(id).textContent = '-';
      });

      console.log("Showing blank metrics - All Images selected");
    }

    function hideLoading() {
      // Loading will be replaced by actual values in updateMetrics
    }

    // Event listeners
    document.getElementById("imageFilter").addEventListener("change", function() {
      console.log("Image filter changed to:", this.value);
      loadDashboard(true); // Preserve selection when filtering
    });

    document.getElementById("projectSearch").addEventListener("input", debounce(function() {
      console.log("Project search changed to:", this.value);
      // Don't reset image selection - let the auto-selection logic handle it
      loadDashboard(false);
    }, 500));

    document.getElementById("dateFilter").addEventListener("change", function() {
      console.log("Date filter changed to:", this.value);
      // Don't reset image selection - let the auto-selection logic handle it
      loadDashboard(false);
    });

    // Debounce function for search input
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func.apply(this, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

// Manual refresh function - Enhanced reset with better clearing
function refreshDashboard(event) {
    console.log("🔄 Reset Data button clicked - clearing all filters");

    // Get the reset button for visual feedback
    const resetButton = event.target;
    const originalText = resetButton.innerHTML;

    try {
        // Show loading state on button
        resetButton.innerHTML = "🔄 Resetting...";
        resetButton.disabled = true;

        // Clear image filter (reset to "All Images")
        const imageFilter = document.getElementById("imageFilter");
        if (imageFilter) {
            imageFilter.value = "";
            console.log("✅ Image filter cleared");
        }

        // Clear project search
        const projectSearch = document.getElementById("projectSearch");
        if (projectSearch) {
            projectSearch.value = "";
            console.log("✅ Project search cleared");
        }

        // Clear date filter
        const dateFilter = document.getElementById("dateFilter");
        if (dateFilter) {
            dateFilter.value = "";
            console.log("✅ Date filter cleared");
        }

        // Clear image status message
        const imageStatus = document.getElementById("imageStatus");
        if (imageStatus) {
            imageStatus.textContent = "";
        }

        console.log("🔄 Reloading dashboard with cleared filters...");

        // Reset images to default state immediately
        showDefaultImages();
        
        // Reset metrics to blank state immediately
        showBlankMetrics();

        // Reload data with cleared filters
        loadDashboard(false);

        // Reset button state after a short delay
        setTimeout(() => {
            resetButton.innerHTML = originalText;
            resetButton.disabled = false;
            console.log("✅ Dashboard reset completed");
        }, 1000);

    } catch (error) {
        console.error("❌ Error during dashboard reset:", error);
        // Reset button state on error
        resetButton.innerHTML = originalText;
        resetButton.disabled = false;
    }
}
   // Set current date
    function updateDate() {
      const now = new Date();

      // Format date
      const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      const dateString = now.toLocaleDateString('en-US', dateOptions);
      document.getElementById('currentDate').textContent = dateString;
    }

    // Initialize dashboard
    console.log("Initializing dashboard...");
    updateDate();
    loadDashboard(false);

    // PDF Download functionality
    async function downloadPDF() {
      const downloadBtn = document.getElementById('pdfDownloadBtn');
      const originalText = downloadBtn.innerHTML;

      try {
        // Show loading state
        downloadBtn.disabled = true;
        downloadBtn.innerHTML = '⏳ Generating PDF...';

        // Get current date for filename
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
        const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS format

        // Get selected image info for filename
        const selectedImage = document.getElementById('imageFilter').value;
        const imageInfo = selectedImage ? `_${selectedImage}` : '_All_Images';

        const filename = `Solar_Panel_Overview${imageInfo}_${dateStr}_${timeStr}.pdf`;

        // Fix for jsPDF global export
        const jsPDF = window.jspdf ? window.jspdf.jsPDF : window.jsPDF;
        if (!jsPDF) {
          alert('jsPDF library not loaded.');
          downloadBtn.disabled = false;
          downloadBtn.innerHTML = originalText;
          return;
        }
        const pdf = new jsPDF('p', 'mm', 'a4');

        // Add title
        pdf.setFontSize(20);
        pdf.setTextColor(74, 144, 226);
        pdf.text('Solar Panel Defect Detection Overview', 20, 20);

        // Add date
        pdf.setFontSize(12);
        pdf.setTextColor(108, 117, 125);
        pdf.text(`Generated on: ${now.toLocaleDateString('en-US', {
          weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'
        })} at ${now.toLocaleTimeString()}`, 20, 30);

        // Add selected filters info
        const projectSearch = document.getElementById('projectSearch').value;
        const dateFilter = document.getElementById('dateFilter').value;
        let filtersText = 'Filters: ';
        if (selectedImage) filtersText += `Image: ${selectedImage}, `;
        if (projectSearch) filtersText += `Project: ${projectSearch}, `;
        if (dateFilter) filtersText += `Date: ${dateFilter}, `;
        if (filtersText === 'Filters: ') filtersText += 'None';
        else filtersText = filtersText.slice(0, -2); // Remove last comma

        pdf.text(filtersText, 20, 40);

        let yPosition = 50;

        // Skip filters section in PDF export (as requested by user)
        // The filters section with dropdowns and search is not included in PDF

        // Check if we need a new page
        if (yPosition > 180) {
          pdf.addPage();
          yPosition = 20;
        }

        // Capture and add images section (now comes first in layout)
        const imagesSection = document.querySelector('.images-section');
        if (imagesSection) {
          const imagesCanvas = await html2canvas(imagesSection, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
          });

          const imagesImgData = imagesCanvas.toDataURL('image/png');
          const imagesWidth = 170;
          const imagesHeight = (imagesCanvas.height * imagesWidth) / imagesCanvas.width;

          // Check if we need a new page
          if (yPosition + imagesHeight > 250) {
            pdf.addPage();
            yPosition = 20;
          }

          pdf.addImage(imagesImgData, 'PNG', 20, yPosition, imagesWidth, imagesHeight);
          yPosition += imagesHeight + 10;
        }

        // Check if we need a new page for metrics
        if (yPosition > 200) {
          pdf.addPage();
          yPosition = 20;
        }

        // Capture and add metrics section (now comes after images)
        const metricsGrid = document.querySelector('.metrics-grid');
        if (metricsGrid) {
          const metricsCanvas = await html2canvas(metricsGrid, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
          });

          const metricsImgData = metricsCanvas.toDataURL('image/png');
          const metricsWidth = 170;
          const metricsHeight = (metricsCanvas.height * metricsWidth) / metricsCanvas.width;

          pdf.addImage(metricsImgData, 'PNG', 20, yPosition, metricsWidth, metricsHeight);
          yPosition += metricsHeight + 10;
        }

        // Add footer
        const pageCount = pdf.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
          pdf.setPage(i);
          pdf.setFontSize(10);
          pdf.setTextColor(108, 117, 125);
          pdf.text(`Page ${i} of ${pageCount} - Solar Panel Overview Report`, 20, 290);
        }

        // Save the PDF
        pdf.save(filename);

        console.log('✅ PDF downloaded successfully:', filename);

      } catch (error) {
        console.error('❌ Error generating PDF:', error);
        alert('Error generating PDF. Please try again.');
      } finally {
        // Restore button state
        downloadBtn.disabled = false;
        downloadBtn.innerHTML = originalText;
      }
    }
  </script>

  <?php include '../PHP/Dronlytics Footer V2.php'; ?>
</body>
</html>

