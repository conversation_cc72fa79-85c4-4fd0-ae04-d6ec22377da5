<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dronlytics Header After Login</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Header After Login.css">
</head>
<body>

  <!-- === HEADER AFTER LOGIN === -->
  <header>
    <div class="header-container">
      <!-- Logo -->
      <div class="logo">
        <img src="https://i.imgur.com/gZXDmtz.png" alt="Dronlytics Logo" class="logo-image">
        <div class="logo-text">DRONLYTICS</div>
      </div>

      <!-- Navigation Bar -->
      <nav>
        <div class="nav-links">
          <a href="../PHP/home.php">Home</a>
          <a href="../PHP/Dronlytics About Us 2.php">About Us</a>

          <!-- Service Dropdown -->
          <div class="dropdown" id="serviceDropdown">
            <a href="javascript:void(0);" onclick="toggleDropdown('serviceDropdown')" aria-haspopup="true"
              aria-expanded="false">Service</a>
            <div class="dropdown-content">
              <a href="../PHP/Dronlytics Images Upload.php">Upload</a>
              <a href="../PHP/Dronlytics History.php">History</a>
            </div>
          </div>

          <!-- Visualization Dropdown -->
          <div class="dropdown" id="visualDropdown">
            <a href="javascript:void(0);" onclick="toggleDropdown('visualDropdown')" aria-haspopup="true"
              aria-expanded="false">Visualization</a>
            <div class="dropdown-content">
              <a href="#" onclick="checkAndRedirect('overview')">Overview</a>
              <a href="#" onclick="checkAndRedirect('dashboard')">Dashboard</a>
            </div>
          </div>

          <!-- Logout Link -->
          <a href="../PHP/Dronlytics Landing Page.php" class="logout-link">
            <svg class="logout-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" />
            </svg>
            Log Out
          </a>
        </div>
      </nav>
    </div>
  </header>

  <!-- === Mobile Menu Toggle Button and Overlay === -->
  <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">☰</div>
  <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobileMenu()"></div>

  <!-- === Mobile Sidebar Menu === -->
  <div class="mobile-sidebar" id="mobileSidebar">
    <a href="../PHP/home.php">Home</a>
    <a href="../PHP/Dronlytics About Us 2.php">About Us</a>

    <!-- Service Dropdown (Mobile) -->
    <div class="mobile-dropdown">
      <span>Service</span>
      <div class="mobile-dropdown-content">
        <a href="../PHP/Dronlytics Images Upload.php">Upload</a>
        <a href="../PHP/Dronlytics History.php">History</a>
      </div>
    </div>

    <!-- Visualization Dropdown (Mobile) -->
    <div class="mobile-dropdown">
      <span>Visualization</span>
      <div class="mobile-dropdown-content">
        <a href="#" onclick="checkAndRedirect('overview')">Overview</a>
        <a href="#" onclick="checkAndRedirect('dashboard')">Dashboard</a>
      </div>
    </div>

    <a href="../PHP/Dronlytics Landing Page.php">Log Out</a>
  </div>

  <!-- === JavaScript === -->
  <script>
    // ===== DROPDOWN TOGGLE =====
    function toggleDropdown(id) {
      document.querySelectorAll('.dropdown').forEach(el => {
        if (el.id !== id) el.classList.remove('show');
      });
      document.getElementById(id).classList.toggle("show");
    }

    // ===== CLOSE DROPDOWNS WHEN CLICKING OUTSIDE =====
    window.addEventListener("click", function (e) {
      document.querySelectorAll(".dropdown").forEach(dropdown => {
        if (!dropdown.contains(e.target)) {
          dropdown.classList.remove("show");
        }
      });
    });

    // ===== CHECK AND REDIRECT FUNCTION =====
    function checkAndRedirect(type) {
      fetch('../PHP/check_upload.php', {
        method: 'GET',
        credentials: 'same-origin', // Include session cookies
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
        .then(res => res.json())
        .then(data => {
          console.log('Upload check result:', data); // Debug log
          if (data.status === 'uploaded') {
            window.location.href = type === 'overview' ? '../PHP/overview.php' : '../PHP/dashboard_mysql.php';
          } else if (data.status === 'not_uploaded') {
            window.location.href = type === 'overview' ? '../PHP/Dronlytics_No_Overview.php' : '../PHP/Dronlytics_No_Dashboard.php';
          } else if (data.status === 'unauthorized') {
            alert("Session expired. Please login again.");
            window.location.href = '../PHP/Dronlytics Login Page.php';
          } else {
            // For any other status, try to go to the page anyway
            console.warn('Unexpected status:', data.status);
            window.location.href = type === 'overview' ? '../PHP/overview.php' : '../PHP/dashboard_mysql.php';
          }
        })
        .catch(error => {
          console.error('Upload check failed:', error);
          // If check fails, still allow access to the page
          window.location.href = type === 'overview' ? '../PHP/overview.php' : '../PHP/dashboard_mysql.php';
        });
    }

    // ===== MOBILE MENU TOGGLE WITH OVERLAY =====
    function toggleMobileMenu() {
      const sidebar = document.getElementById("mobileSidebar");
      const overlay = document.getElementById("mobileOverlay");
      const isOpen = sidebar.classList.contains("open");

      if (isOpen) {
        sidebar.classList.remove("open");
        overlay.classList.remove("show");
      } else {
        sidebar.classList.add("open");
        overlay.classList.add("show");
      }
    }

    // ===== CLOSE MOBILE MENU =====
    function closeMobileMenu() {
      document.getElementById("mobileSidebar").classList.remove("open");
      document.getElementById("mobileOverlay").classList.remove("show");
    }

    // ===== CLOSE SIDEBAR IF CLICKED OUTSIDE (ON MOBILE) =====
    document.addEventListener("click", function (e) {
      const sidebar = document.getElementById("mobileSidebar");
      const toggle = document.querySelector(".mobile-menu-toggle");

      if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
        sidebar.classList.remove("open");
        document.getElementById("mobileOverlay").classList.remove("show");
      }
    });
  </script>

</body>
</html>

