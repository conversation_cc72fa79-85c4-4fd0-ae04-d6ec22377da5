/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESETS & BODY STYLING === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  background: rgba(74, 144, 226, 0.03);
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

html {
  scroll-behavior: smooth;
}


/* === FOOTER CONTAINER === */
footer {
  background: #002f5f;
  text-align: center;
  padding: 0.75rem 0;
  margin-top: auto;
  width: 100%;
  position: relative;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* === FOOTER TEXT === */
.footer-note {
  color: white;
  font-size: 1rem;
  font-weight: 400;
  opacity: 1;
  margin: 0;
}

