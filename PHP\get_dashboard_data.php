<?php
$mysqli = new mysqli("localhost", "root", "", "userdb");
if ($mysqli->connect_error) {
    die(json_encode(["error" => "DB Connection failed."]));
}

$user = $_GET['user'] ?? 'U021';

// Get images with annotated paths (processed images)
$stmt = $mysqli->prepare("
    SELECT DISTINCT iu.Image_ID
    FROM image_uploaded iu
    WHERE iu.User_ID = ? AND iu.Annotated_Image_Path IS NOT NULL
");
$stmt->bind_param("s", $user);
$stmt->execute();
$result = $stmt->get_result();

$data = [];
while ($row = $result->fetch_assoc()) {
    $image_id = $row["Image_ID"];

    // Calculate panel metrics from defects for this image
    $defect_query = "SELECT SUM(Defect_Count) as total_defects FROM image_defects WHERE Image_ID = ?";
    $defect_stmt = $mysqli->prepare($defect_query);
    $defect_stmt->bind_param("s", $image_id);
    $defect_stmt->execute();
    $defect_result = $defect_stmt->get_result();
    $defect_data = $defect_result->fetch_assoc();

    // Assume 100 total panels (you can adjust this based on your requirements)
    $total_panels = 100;
    $defect_panels = (int)($defect_data['total_defects'] ?? 0);
    $healthy_panels = max(0, $total_panels - $defect_panels);
    $defective_rate = $total_panels > 0 ? round(($defect_panels / $total_panels) * 100, 2) : 0;

    $data[] = [
        "Image_ID" => $image_id,
        "Total_Panel_Count" => $total_panels,
        "Healthy_Panel_Count" => $healthy_panels,
        "Defect_Panel_Count" => $defect_panels,
        "Defective_Rate" => $defective_rate . '%'
    ];

    $defect_stmt->close();
}

echo json_encode($data);
?>
