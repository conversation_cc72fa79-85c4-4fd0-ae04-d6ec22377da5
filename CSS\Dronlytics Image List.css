/* === GOOGLE FONTS IMPORT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');


/* === GLOBAL RESET === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
  background: rgba(74, 144, 226, 0.03);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

html {
  scroll-behavior: smooth;
}


/* === MAIN CONTENT === */
.main-content {
  flex: 1;
  padding: 120px 2rem 0.5cm 2rem;
  min-height: calc(100vh - 120px);
  background: rgba(74, 144, 226, 0.05);
}


/* === UPLOAD LIST CONTAINER STYLING === */
.upload-list-container {
  background: white;
  padding: 3rem 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  text-align: center;
  max-width: 1000px;
  width: 100%;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
  margin: 0 auto 2rem auto;
}

.upload-list-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
}


/* === PAGE TITLE STYLING === */
.page-title {
  font-size: 2rem;
  color: #1e3a5f;
  margin-bottom: 2rem;
  font-weight: 700;
  letter-spacing: 1px;
}


/* === IMAGE GRID LAYOUT === */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 3rem;
}


/* === INDIVIDUAL IMAGE BOX STYLING === */
.image-box {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  background-color: white;
  text-align: center;
  width: 160px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.image-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #4a90e2;
}

.image-box img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 1rem;
}


/* === BUTTON STYLING === */
.btn {
  display: block;
  margin: 0.5rem auto;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  background: linear-gradient(135deg, #4a90e2, #002f5f);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}


/* === REMOVE BUTTON STYLING === */
.btn.remove-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.btn.remove-btn:hover {
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}


/* === UPLOAD BUTTON STYLING === */
#uploadBtn {
  margin: 2rem auto 0;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
}

/* === UPLOAD STATUS STYLING === */
#uploadStatus {
  margin-top: 1.5rem;
  font-weight: 600;
  color: #4a90e2;
  font-size: 1.1rem;
}


/* === RESPONSIVE BEHAVIOUR === */
@media (max-width: 768px) {
  .main-content {
    padding: 100px 1rem 6rem 1rem;
  }

  .upload-list-container {
    margin-top: 3rem;
    padding: 2rem 1.5rem;
    margin-bottom: -1.5rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .image-grid {
    gap: 1rem;
  }

  .image-box {
    width: 140px;
  }

  .image-box img {
    width: 100px;
    height: 100px;
  }
}
