<?php
// image_proxy.php - Fast proxy with caching for Google Drive images
$fileId = $_GET['id'] ?? '';
$size = $_GET['size'] ?? 'medium'; // small, medium, large

if (empty($fileId)) {
    http_response_code(400);
    echo "Missing file ID";
    exit;
}

// Create cache directory
$cacheDir = '../cache/images';
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0755, true);
}

// Cache file path
$cacheFile = "{$cacheDir}/{$fileId}_{$size}.jpg";

// Check if cached version exists and is recent (24 hours)
if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < 86400) {
    // Serve from cache
    header('Content-Type: image/jpeg');
    header('Cache-Control: public, max-age=86400'); // Cache for 24 hours
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 86400) . ' GMT');
    readfile($cacheFile);
    exit;
}

// Size-specific URLs for faster loading
$sizeMap = [
    'small' => 'w400',
    'medium' => 'w800',
    'large' => 'w1200'
];
$sizeParam = $sizeMap[$size] ?? 'w800';

// Try different Google Drive URL formats (optimized order)
$urls = [
    "https://drive.google.com/thumbnail?id={$fileId}&sz={$sizeParam}",
    "https://lh3.googleusercontent.com/d/{$fileId}={$sizeParam}",
    "https://drive.google.com/uc?export=download&id={$fileId}"
];

foreach ($urls as $url) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 8,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'follow_location' => true,
            'max_redirects' => 3
        ]
    ]);

    $imageData = @file_get_contents($url, false, $context);

    if ($imageData !== false && strlen($imageData) > 1000) {
        // Save to cache
        file_put_contents($cacheFile, $imageData);

        // Serve image
        header('Content-Type: image/jpeg');
        header('Cache-Control: public, max-age=86400');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 86400) . ' GMT');
        echo $imageData;
        exit;
    }
}

// If all formats fail, return error
http_response_code(404);
header('Content-Type: text/plain');
echo "Image not found";
?>
