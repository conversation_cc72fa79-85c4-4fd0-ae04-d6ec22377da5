<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Dronlytics - Smart Solar Panel Inspection</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Landing Page.css" /> 
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>

  <!-- HEADER -->
  <?php include '../PHP/Dronlytics Before Login Header.php'; ?>

  <!-- HERO -->
  <section class="hero" id="home">
    <div class="hero-content">
      <div class="welcome-text">WELCOME TO</div>
      <h1>DRONLYTICS</h1>
      <p>A powerful fusion of drone technology and advanced image analytics for thorough solar panel inspections, driving smarter, IoT-enabled maintenance decisions.</p>
      <a href="#account" class="cta-button">Login | Register</a>
    </div>
    <div class="hero-waves"></div>
  </section>

  <!-- SERVICE VISUALS -->
  <section class="service-visuals section fade-in">
    <div class="container">
      <h2 class="section-title">WHAT WE OFFER?</h2>
      <p class="section-subtitle">Explore How Our Solar Inspection System Operates</p>

      <div class="visuals-grid">
        <div class="visual-card">
          <div class="visual-media">
            <img src="https://images.unsplash.com/photo-**********-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="User uploading drone images" />
          </div>
          <h3>Image Upload System</h3>
          <p>Users upload images captured by their own drones, which our platform processes to provide detailed analysis and inspection reports.</p>
        </div>

        <div class="visual-card">
          <div class="visual-media">
            <img src="https://images.unsplash.com/photo-**********-aa79dcee981c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="AI-powered defect detection analysis" />
          </div>
          <h3>AI-Powered Defect Detection</h3>
          <p>Accurate identification of solar panel defects using advanced image analysis—detect cracks, dust, bird drops, and more from user-uploaded drone images.</p>
        </div>

        <div class="visual-card">
          <div class="visual-media">
            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Analytics dashboard" />
          </div>
          <h3>Data Dashboard</h3>
          <p>Comprehensive analytics and detailed reports delivered through our intelligent platform for informed decision-making.</p>
        </div>

        <div class="visual-card">
          <div class="visual-media">
            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Centralized solar panel monitoring" />
          </div>
          <h3>Centralized Monitoring</h3>
          <p>Centralized monitoring system dedicated to managing multiple solar sites, streamlining inspection records and optimizing maintenance schedules.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- FOOTER -->
  <?php include '../PHP/Dronlytics Long Footer.php'; ?>

  <!-- JAVASCRIPT -->
  <script>
    /* DOM READY */
    document.addEventListener('DOMContentLoaded', function () {
      
      /* SMOOTH SCROLLING */
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            const headerHeight = 100;
            const targetPosition = target.offsetTop - headerHeight;
            
            window.scrollTo({
              top: targetPosition,
              behavior: 'smooth'
            });
          }
        });
      });

      /* FADE-IN ANIMATIONS */
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      }, observerOptions);

      // Observe all fade-in elements
      document.querySelectorAll('.fade-in').forEach(el => {
        observer.observe(el);
      });

      /* EMAIL SUBSCRIPTION */
      const emailInput = document.querySelector('.email-subscribe input');
      const emailButton = document.querySelector('.email-subscribe button');

      if (emailInput && emailButton) {
        emailButton.addEventListener('click', function() {
          const email = emailInput.value.trim();
          if (email && isValidEmail(email)) {
            // Simulate subscription success
            emailButton.textContent = '✓';
            emailButton.style.background = 'linear-gradient(135deg, #48bb78, #38a169)';
            emailInput.value = '';
            
            setTimeout(() => {
              emailButton.textContent = '→';
              emailButton.style.background = '#4a90e2';
            }, 2000);
          } else {
            // Show error state
            emailInput.style.borderColor = '#e53e3e';
            emailInput.placeholder = 'Please enter a valid email';
            
            setTimeout(() => {
              emailInput.style.borderColor = '';
              emailInput.placeholder = 'Enter your email address';
            }, 3000);
          }
        });

        // Allow Enter key to submit email
        emailInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            emailButton.click();
          }
        });
      }

      /* EMAIL VALIDATION */
      function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      }

      /* BUTTON LOADING */
      document.querySelectorAll('.cta-button, .login-button').forEach(button => {
        button.addEventListener('click', function(e) {
          if (this.getAttribute('href') === '#account') {
            e.preventDefault();

            const originalContent = this.innerHTML;
            this.innerHTML = '<span style="display: inline-block; animation: spin 1s linear infinite;">⟳</span> Loading...';
            this.style.pointerEvents = 'none';

            setTimeout(() => {
              this.innerHTML = originalContent;
              this.style.pointerEvents = 'auto';
              // Redirect to login page
              window.location.href = '../PHP/Dronlytics Login Page.php';
            }, 2000);
          } else {
            // Add loading animation for other buttons
            const originalContent = this.innerHTML;
            this.innerHTML = '<span style="display: inline-block; animation: spin 1s linear infinite;">⟳</span> Loading...';
            this.style.pointerEvents = 'none';

            // Allow the page to redirect after a brief loading animation
            setTimeout(() => {
              this.style.pointerEvents = 'auto';
              // The href will handle the actual navigation
            }, 500);
          }
        });
      });

      /* PARALLAX EFFECT */
      window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero');
        const heroContent = document.querySelector('.hero-content');
        
        if (hero && heroContent && scrolled < hero.offsetHeight) {
          heroContent.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
      });

      /* TYPEWRITER EFFECT */
      const heroTitle = document.querySelector('.hero h1');
      if (heroTitle) {
        const originalText = heroTitle.textContent;
        heroTitle.textContent = '';
        
        let i = 0;
        const typeWriter = () => {
          if (i < originalText.length) {
            heroTitle.textContent += originalText.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
          }
        };
        
        // Start typewriter effect after page load
        setTimeout(typeWriter, 1000);
      }

      /* MOUSE PARALLAX */
      document.addEventListener('mousemove', function(e) {
        const particles = document.querySelectorAll('.particle');
        const x = e.clientX / window.innerWidth;
        const y = e.clientY / window.innerHeight;

        particles.forEach((particle, index) => {
          const speed = (index + 1) * 0.5;
          const xPos = (x - 0.5) * speed * 50;
          const yPos = (y - 0.5) * speed * 50;
          
          particle.style.transform = `translate(${xPos}px, ${yPos}px)`;
        });
      });

      /* SCROLL PROGRESS */
      const scrollProgress = document.createElement('div');
      scrollProgress.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 4px;
        background: linear-gradient(90deg, #4299e1, #63b3ed);
        z-index: 9999;
        transition: width 0.1s ease;
      `;
      document.body.appendChild(scrollProgress);

      window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        scrollProgress.style.width = scrollPercent + '%';
      });

      /* ANIMATIONS */
      const style = document.createElement('style');
      style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);

    });

    /* PERFORMANCE */
    // Throttle scroll events for better performance
    function throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      }
    }

    // Apply throttling to scroll events
    window.addEventListener('scroll', throttle(function() {
      // Scroll-based animations can be added here if needed
    }, 16)); // ~60fps
  </script>
  
</body>
</html>