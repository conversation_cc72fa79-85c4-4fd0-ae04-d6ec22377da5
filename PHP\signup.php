<?php
session_start();

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: ../PHP/Dronlytics Sign Up Page.php");
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "userdb");
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get and validate form data
$fullname = trim($_POST['fullname']);
$email = trim($_POST['email']);
$password = $_POST['password'];
$confirmPassword = $_POST['confirm-password'];

// Validation
$errors = [];

if (empty($fullname)) {
    $errors[] = "Full name is required";
}

if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Valid email is required";
}

if (strlen($password) < 6) {
    $errors[] = "Password must be at least 6 characters";
}

if ($password !== $confirmPassword) {
    $errors[] = "Passwords do not match";
}

// Check if email already exists
$checkEmail = $conn->prepare("SELECT Email FROM user_data WHERE Email = ?");
$checkEmail->bind_param("s", $email);
$checkEmail->execute();
$result = $checkEmail->get_result();
if ($result->num_rows > 0) {
    $errors[] = "Email already exists";
}

// If there are errors, redirect back with error message
if (!empty($errors)) {
    $_SESSION['signup_errors'] = $errors;
    $_SESSION['form_data'] = $_POST;
    header("Location: ../PHP/Dronlytics Sign Up Page.php");
    exit();
}

// Hash password
$hashedPassword = password_hash($password, PASSWORD_BCRYPT);

// Get new user ID
$result = $conn->query("SELECT MAX(User_ID) as max_id FROM user_data");
if ($result && $row = $result->fetch_assoc()) {
    $maxId = $row['max_id'];
    $nextId = "U" . str_pad((intval(substr($maxId, 1)) + 1), 3, "0", STR_PAD_LEFT);
} else {
    $nextId = "U001";
}

// Insert new user
$stmt = $conn->prepare("INSERT INTO user_data (User_ID, fullname, Email, Password) VALUES (?, ?, ?, ?)");
if (!$stmt) {
    $_SESSION['signup_errors'] = ["Database error occurred"];
    header("Location: ../PHP/Dronlytics Sign Up Page.php");
    exit();
}

$stmt->bind_param("ssss", $nextId, $fullname, $email, $hashedPassword);

if ($stmt->execute()) {
    $_SESSION['signup_success'] = "Account created successfully! Please log in.";
    header("Location: ../PHP/Dronlytics Login Page.php");
} else {
    $_SESSION['signup_errors'] = ["Failed to create account"];
    header("Location: ../PHP/Dronlytics Sign Up Page.php");
}

$stmt->close();
$conn->close();
?>
