<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: ../PHP/Dronlytics Login Page.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Upload Successful - Dronlytics</title>
  <link rel="stylesheet" href="../CSS/Dronlytics Upload Successful.css"> 
</head>
<body>

<!-- === HEADER === -->
<?php include '../PHP/Dronlytics Header After Login.php'; ?>

  <!-- === MAIN CONTENT === -->
  <div class="main-content">
    <div class="success-container">
      <div class="check-icon">✓</div>
      <h2 class="success-title">Upload Successful!</h2>
      <p class="success-subtitle">Your drone image has been uploaded and is now being processed.</p>
      <div class="action-buttons">
        <?php
          // Get the last uploaded image ID for this user
          include_once '../PHP/db.php';
          $user_id = $_SESSION['user_id'];
          $img_stmt = $conn->prepare("SELECT Image_ID FROM image_uploaded WHERE User_ID = ? ORDER BY Upload_Timestamp DESC, Image_ID DESC LIMIT 1");
          $img_stmt->bind_param("s", $user_id);
          $img_stmt->execute();
          $img_stmt->bind_result($last_image_id);
          $img_stmt->fetch();
          $img_stmt->close();
          $conn->close();
        ?>
        <a href="../PHP/overview.php<?php echo isset($last_image_id) && $last_image_id ? '?image_id=' . urlencode($last_image_id) : ''; ?>" class="primary-button" target="_blank">View Results</a>
        <a href="../PHP/Dronlytics Images Upload.php" class="secondary-button">Upload More Images</a>
      </div>
    </div>
  </div>

  <!-- === FOOTER === -->
  <?php include '../PHP/Dronlytics Footer V2.php'; ?>

</body>
</html>
