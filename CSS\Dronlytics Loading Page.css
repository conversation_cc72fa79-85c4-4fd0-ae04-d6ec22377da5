/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET === */
* { 
  margin: 0; 
  padding: 0; 
  box-sizing: border-box; 
}

/* === BODY STYLES === */
body {
  font-family: 'Inter', sans-serif;
  background: rgba(74, 144, 226, 0.03);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* === MAIN CONTENT STYLES === */
.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120px 2rem 6rem 2rem;
  background: rgba(74, 144, 226, 0.05);
}

/* === LOADING CONTAINER STYLES === */
.loading-container {
  background: white;
  padding: 4rem 3rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  text-align: center;
  max-width: 500px;
  width: 100%;
  position: relative;
}

.loading-container::before {
  content: '';
  position: absolute;
  top: 0; 
  left: 0; 
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
}

/* === SPINNER STYLES === */
.spinner {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #4a90e2;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === LOADING TEXT STYLES === */
.loading-title {
  font-size: 1.8rem;
  color: #1e3a5f;
  margin-bottom: 1rem;
  font-weight: 700;
}

.loading-subtitle {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1.5rem;
}

/* === PROGRESS BAR STYLES === */
.loading-progress {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.loading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
  animation: progress 2s ease-in-out;
}

@keyframes progress {
  0% { width: 0%; }
  100% { width: 100%; }
}


/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .main-content { 
    padding: 100px 1rem; 
  }
  
  .loading-container { 
    padding: 3rem 2rem; 
  }
  
  .loading-title { 
    font-size: 1.5rem; 
  }
}
