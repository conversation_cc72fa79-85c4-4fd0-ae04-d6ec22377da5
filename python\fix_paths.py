#!/usr/bin/env python3
"""
Fix annotated image paths to use forward slashes for web compatibility
"""

import mysql.connector

DB_CONFIG = {
    'host': 'localhost', 'user': 'root', 'password': '',
    'database': 'userdb', 'charset': 'utf8mb4', 'autocommit': True
}

def main():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Fix paths to use forward slashes
        cursor.execute("""
            UPDATE image_uploaded 
            SET Annotated_Image_Path = REPLACE(Annotated_Image_Path, '\\\\', '/') 
            WHERE Annotated_Image_Path IS NOT NULL
        """)
        conn.commit()
        
        # Verify the updated paths
        cursor.execute('SELECT Image_ID, Annotated_Image_Path FROM image_uploaded WHERE Annotated_Image_Path IS NOT NULL')
        results = cursor.fetchall()
        print('Fixed annotated image paths:')
        for row in results:
            print(f'  {row[0]}: {row[1]}')
        
        conn.close()
        print('✅ Paths fixed successfully!')
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
