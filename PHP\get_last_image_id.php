<?php
header("Content-Type: application/json");

$conn = new mysqli("localhost", "root", "", "userdb");

if ($conn->connect_error) {
    echo json_encode(["error" => "Database connection failed"]);
    exit();
}

$result = $conn->query("SELECT Image_ID FROM image_uploaded ORDER BY Image_ID DESC LIMIT 1");

if ($result && $row = $result->fetch_assoc()) {
    echo json_encode(["last_id" => $row["Image_ID"]]);
} else {
    echo json_encode(["last_id" => "IMG000"]);
}

$conn->close();
?>