<?php
include("db.php");
include("flask_auto_starter.php");

$data = json_decode(file_get_contents("php://input"), true);

$imageId = $data['image_id'];
$imagePath = $data['image_path'];
$userId = $data['user_id'];
$projectName = $data['project_name']; // NEW

// First, check if the user exists in user_data table
$userCheckStmt = $conn->prepare("SELECT User_ID FROM user_data WHERE User_ID = ?");
$userCheckStmt->bind_param("s", $userId);
$userCheckStmt->execute();
$userResult = $userCheckStmt->get_result();

if ($userResult->num_rows === 0) {
    // User doesn't exist, return error with helpful message
    echo json_encode([
        "status" => "error",
        "message" => "User ID '$userId' not found in database. Please log in again.",
        "error_type" => "invalid_user_id"
    ]);
    $userCheckStmt->close();
    $conn->close();
    exit;
}
$userCheckStmt->close();

// User exists, proceed with image upload
$stmt = $conn->prepare("INSERT INTO image_uploaded (Image_ID, Image_Path, User_ID, Project_Name) VALUES (?, ?, ?, ?)");
$stmt->bind_param("ssss", $imageId, $imagePath, $userId, $projectName);

if ($stmt->execute()) {
    // ✅ Image saved successfully - NOW trigger automatic ML processing
    $response = ["status" => "success", "image_id" => $imageId];

    // 📝 Log successful upload
    error_log("📷 Image $imageId uploaded successfully to database");

    // 🚀 Trigger Flask app and image processing
    error_log("🚀 Triggering automatic image processing for $imageId");

    // Start processing in background (non-blocking)
    $processing_success = triggerImageProcessing();

    if ($processing_success) {
        error_log("✅ Image processing triggered successfully for $imageId");
        $response["processing_status"] = "triggered";
    } else {
        error_log("⚠️ Failed to trigger image processing for $imageId");
        $response["processing_status"] = "failed";
        $response["processing_message"] = "Image uploaded but processing failed to start. Please try manual processing.";
    }

    echo json_encode($response);
} else {
    echo json_encode([
        "status" => "error",
        "message" => $stmt->error,
        "error_type" => "database_insert_failed"
    ]);
}

$stmt->close();
$conn->close();
?>
