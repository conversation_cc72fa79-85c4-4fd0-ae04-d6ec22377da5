/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');


/* === GLOBAL RESETS & BODY STYLING === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  background: rgba(74, 144, 226, 0.03);
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

html {
  scroll-behavior: smooth;
}


/* === HEADER SECTION === */
header {
  background: #002f5f;
  padding: 0.75rem 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}


/* === LOGO === */
.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 8px;
  padding: 4px;
}

.logo-text {
  font-size: 1.6rem;
  font-weight: 700;
  color: white;
}


/* === NAVIGATION BAR === */
nav {
  display: flex;
  justify-content: flex-end;
  gap: 1.5rem;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}

nav a {
  text-decoration: none;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

nav a:hover {
  color: #4a90e2;
  background: rgba(255, 255, 255, 0.1);
}


/* === DROPDOWN MENU === */
.dropdown {
  position: relative;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  background: #2a4a6f;
  border-radius: 8px;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  min-width: 180px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  display: none;
}

.dropdown.show .dropdown-content {
  display: block;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-content a {
  color: white;
  padding: 0.6rem 0.8rem;
  display: block;
  font-size: 0.9rem;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.dropdown-content a:last-child {
  border-bottom: none;
}

.dropdown-content a:hover {
  background: rgba(74, 144, 226, 0.2);
  color: #4a90e2;
}


/* === LOGOUT LINK === */
.logout-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-icon {
  width: 20x;
  height: 20px;
  fill: currentColor;
}


/* === RESPONSIVE MENU TOGGLE BUTTON === */
.mobile-menu-toggle {
  display: none;
  font-size: 1.7rem;
  color: white;
  cursor: pointer;
  z-index: 1100;
  margin-right: 1.5rem;
  padding: 0.6rem 0.5rem;
  transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease; 
  border-radius: 6px;
}

.mobile-menu-toggle:hover {
  transform: scale(1.1);
}

/* === HAMBURGER (mobile toggle) === */
.mobile-menu-toggle {
  display: none;                 /* shown in mobile via media query */
  position: fixed;
  top: 14px;
  right: 14px;                   /* hamburger on the right */
  left: auto;
  font-size: 1.7rem;
  color: #fff;
  cursor: pointer;
  z-index: 1100;
  padding: 0.6rem 0.5rem;
  border-radius: 6px;
  transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease;
}
.mobile-menu-toggle:hover { transform: scale(1.1); }

/* === MOBILE SIDEBAR (slides in from right) === */
.mobile-sidebar {
  position: fixed;
  top: 0;
  right: -260px;                 /* hidden off-screen to the right */
  width: 260px;
  height: 100%;
  background: #002f5f;
  box-shadow: -2px 0 10px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  padding: 4rem 1.5rem;
  gap: 1.5rem;
  transition: right 0.3s ease;
  z-index: 1050;
}
.mobile-sidebar.open { right: 0; }

.mobile-sidebar a {
  color: #fff;                   /* white links */
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 6px;
}
.mobile-sidebar a:hover { background: rgba(255,255,255,0.1); }

/* === OVERLAY BEHIND SIDEBAR === */
.mobile-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.mobile-overlay.show { opacity: 1; visibility: visible; }

/* === MOBILE DROPDOWNS (sections + items) === */
.mobile-dropdown { position: relative; margin-bottom: 8px; }

.mobile-dropdown > span {
  display: block;
  font-weight: 600;
  padding: 8px 16px;
  color: #4a90e2;               /* section titles: Service / Visualization */
  cursor: pointer;
}

.mobile-dropdown-content { display: block; padding-left: 24px; }

/* Submenu items: Upload, History, Overview, Dashboard */
.mobile-dropdown-content a {
  display: block;
  color: #fff;                  /* force white text */
  padding: 4px 0;
  text-decoration: none;
}
.mobile-dropdown-content a:hover { color: #dbeafe; }

/* === RESPONSIVE BEHAVIOR (<= 1024px, covers 400x642) === */
@media (max-width: 1024px) {
  /* hide desktop nav */
  nav { display: none !important; }

  /* show hamburger (fixed right) */
  .mobile-menu-toggle {
    display: block !important;
    margin: 0;                  /* override any previous margins */
  }

  /* keep logo + DRONLYTICS pinned left */
  .header-container {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* brand left */
    gap: 1rem;
    padding-right: 64px;         /* space for the fixed hamburger */
  }
  .logo {
    margin-right: auto;          /* push hamburger to the far right */
  }
}

/* == Normalize header height & brand alignment on mobile (≤1024px) == */
@media (max-width: 1024px) {
  /* lock a consistent header size */
  header {
    height: 64px;
    padding: 0 !important;
  }

  .header-container {
    height: 64px;
    padding: 0 12px !important;     /* tighter left padding so brand sits closer to edge */
    display: flex;
    align-items: center;             /* vertical center */
    justify-content: flex-start;     /* keep brand left */
    gap: 10px;
    padding-right: 64px !important;  /* reserve space for hamburger */
  }

  /* BRAND — kill any inherited spacing/centering */
  .logo,
  .logo a {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 8px;                        /* spacing between icon and text */
    margin: 0 !important;
    padding: 0 !important;
    text-align: left !important;
  }
  .logo { margin-right: auto !important; }  /* push hamburger to far right */

  .logo-image {
    height: 30px;                    /* slightly larger for balance */
    width: auto !important;
    padding: 0 !important;
    display: block;
  }

  .logo-text {
    font-size: 21px;                 /* scale to match icon */
    line-height: 30px;               /* align baseline with icon height */
    margin: 0 !important;
    color: #fff;
    display: inline-flex;
    align-items: center;             /* perfect vertical alignment with icon */
  }

  /* HAMBURGER — fixed right & vertically centered in header */
  .mobile-menu-toggle {
    display: block !important;
    position: fixed;
    right: 12px;
    top: calc(64px / 2);
    transform: translateY(-50%);
    left: auto;
    margin: 0 !important;
    z-index: 1100;
  }

  /* hide desktop nav on mobile */
  nav { display: none !important; }

  /* Ensure mobile dropdown submenu links are white */
  .mobile-dropdown-content a { color: #fff; }
  .mobile-dropdown-content a:hover { color: #dbeafe; }
}

/* == Mobile hard‑pin: brand left & perfectly centered (≤1024px) == */
@media (max-width: 1024px) {
  /* lock header size */
  header {
    height: 64px;
    padding: 0 !important;
  }

  /* anchor for absolute brand */
  .header-container {
    position: relative;           /* <-- key */
    height: 64px;
    padding: 0 16px !important;
  }

  /* BRAND: absolute + centered vertically, left-aligned */
  .logo,
  .logo a {
    position: absolute !important;
    top: 50%;
    left: 12px;                   /* adjust if you want closer/farther */
    transform: translateY(-50%);
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 8px;
    margin: 0 !important;
    padding: 0 !important;
    text-align: left !important;
    width: auto !important;
  }
  .logo-image { height: 30px; width: auto !important; display: block; }
  .logo-text  { font-size: 21px; line-height: 1; margin: 0 !important; color: #fff; }

  /* HAMBURGER: fixed right & centered to header height */
  .mobile-menu-toggle {
    display: block !important;
    position: fixed;
    right: 12px;
    top: 32px;                    /* 64px / 2 */
    transform: translateY(-50%);
    left: auto;
    margin: 0 !important;
    z-index: 1100;
  }

  /* hide desktop nav on mobile */
  nav { display: none !important; }

  /* sidebar submenu links white */
  .mobile-dropdown-content a { color: #fff; }
  .mobile-dropdown-content a:hover { color: #dbeafe; }
}
