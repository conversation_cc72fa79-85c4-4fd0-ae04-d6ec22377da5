/* === GOOGLE FONTS IMPORT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === GLOBAL RESET === */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* === BODY STYLING === */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow: hidden;
  background: rgba(74, 144, 226, 0.03);
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

/* === HTML SCROLL BEHAVIOR === */
html {
  scroll-behavior: smooth;
}


/* === MAIN CONTENT === */
.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2rem;
  background: rgba(74, 144, 226, 0.05);
  position: relative;
  transform: translateY(30px);
}


/* === DASHBOARD CONTAINER === */
.dashboard-container {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  text-align: center;
  max-width: 700px;
  width: 100%;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4a90e2, #002f5f);
}


/* === DASHBOARD TITLE AND SUBTITLE === */
.dashboard-title {
  font-size: 1.6rem;
  color: #1e3a5f;
  margin-bottom: 1rem;
  font-weight: 700;
  letter-spacing: 1px;
}


.dashboard-subtitle {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}


/* === STATUS MESSAGE === */
.status-message {
  background: rgba(255, 193, 7, 0.1);
  border: 2px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}


/* === STATUS ICON === */
.status-icon {
  font-size: 2.5rem;
  margin-bottom: 0.8rem;
}


/* === STATUS TEXT === */
.status-text {
  font-size: 1.2rem;
  color: #1e3a5f;
  font-weight: 600;
  margin-bottom: 0.8rem;
}


/* === STATUS DESCRIPTION === */
.status-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}


/* === ACTION BUTTONS === */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.btn-primary {
  background: linear-gradient(135deg, #4a90e2, #002f5f);
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}


/* === RESPONSIVE BEHAVIOR === */
:root{
  --header-h-mobile: 64px; /* change if your mobile header isn't 64px */
  --gap-bg-color: rgba(74, 144, 226, 0.03); /* lighter colour you want */
}

@media (max-width: 1024px) {
  /* allow scrolling */
  html, body {
    height: auto !important;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
    background: var(--gap-bg-color) !important; /* only affects the body background */
  }

  header { height: var(--header-h-mobile); }

  /* make gaps the lighter colour */
  .main-content,
  .content-area,
  .page-content {
    transform: none !important;
    margin-top: calc(var(--header-h-mobile) + 0.5cm) !important; /* below header */
    margin-bottom: 0.5cm !important; /* above footer */
    padding-left: 1rem;
    padding-right: 1rem;
    background: var(--gap-bg-color); /* match lighter body background */
  }

  /* Keep dashboard padding */
  .dashboard-container { padding: 2rem 1.5rem; }
}
