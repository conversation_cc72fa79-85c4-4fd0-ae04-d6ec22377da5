<?php
session_start();
$conn = new mysqli("localhost", "root", "", "userdb");

$email = $_POST['email'];
$password = $_POST['password'];

$stmt = $conn->prepare("SELECT * FROM user_data WHERE Email = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
  if (password_verify($password, $row['Password'])) {
    $_SESSION['username'] = $row['Fullname'];
    $_SESSION['user_id'] = $row['User_ID'];
    header("Location: ../PHP/home.php");
    exit();
  }
}

echo "Invalid email or password.";
?>
