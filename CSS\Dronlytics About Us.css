/* === GOOGLE FONT === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* === CSS VARIABLES === */
:root {
  --primary-blue: #1a365d;
  --secondary-blue: #2d5a87;
  --accent-blue: #4299e1;
  --light-blue: #63b3ed;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-light: #718096;
  --surface-white: #ffffff;
  --surface-gray: #f7fafc;
  --shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* === GLOBAL RESET (Only if not included elsewhere) === */

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  overflow-x: hidden;
  background: var(--surface-white);
}

/* === LAYOUT === */
.section {
  padding: 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* === ABOUT SECTION === */
.about-section {
  background: white;
  text-align: center;
  padding-top: 8rem;
}


  .about-us-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-blue);
    letter-spacing: 0.05em;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    line-height: 1.1;
  }
  
  .tagline {
    font-size: 1.3rem;
    color: #4a90e2;
    font-weight: 600;
    margin: 2rem 0;
  }
  
  .about-text {
    font-size: 1rem;
    color: #666;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* === SERVICES GRID === */
  .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
  }
  
  .service-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
  }
  
  .service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
  }
  
  .service-icon {
    width: 80px;
    height: 80px;
    background: #002f5f;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
  }
  
  .service-card h3 {
    font-size: 1.2rem;
    color: #1e3a5f;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  .service-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.6;
  }

/* === VISION MISSION SECTION === */
.vision-mission {
    background: #f8f9fa;
    padding-top: 1.7cm; /* Move title and subtitle down by 1.7cm */
  }
  
  .vm-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 3rem;
    align-items: stretch;
    margin-top: 3rem;
  }
  
  .vm-image {
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(30, 58, 95, 0.1));
    border-radius: 8px;
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
  }
  
  .vm-card {
    background: white;
    padding: 2.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    text-align: center;
    border-left: 4px solid #4a90e2;
  }
  
  .vm-card h3 {
    font-size: 1.5rem;
    color: #1e3a5f;
    margin-bottom: 1.5rem;
    font-weight: 600;
    letter-spacing: 1px;
  }
  
  .vm-card p {
    color: #666;
    line-height: 1.7;
  }
  
/* === CORE VALUES SECTION === */
  .core-values {
    background: white;
  }
  
  .values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
  }
  
  .value-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    position: relative;
    overflow: hidden;
  }
  
  .value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4a90e2, #1e3a5f);
  }
  
  .value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
  }
  
  .value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4a90e2, #1e3a5f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
  }
  
  .value-card h3 {
    font-size: 1.3rem;
    color: #1e3a5f;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  .value-card p {
    color: #666;
    line-height: 1.6;
  }
  
/* === WHY CHOOSE US SECTION === */
  .why-choose-us {
    background: #f8f9fa;
  }
  
  .why-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    margin-top: 3rem;
  }
  
  .why-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    position: relative;
    overflow: hidden;
  }
  
  .why-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4a90e2, #002f5f);
  }
  
  .why-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
  }
  
  .why-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, #4a90e2, #002f5f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2.2rem;
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
  }
  
  .why-card h3 {
    font-size: 1.4rem;
    color: #1e3a5f;
    margin-bottom: 1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
  
  .why-card p {
    color: #666;
    line-height: 1.7;
    font-size: 0.95rem;
  }
  
/* === RESPONSIVE DESIGN === */
  @media (max-width: 768px) {
    .hero {
      padding: 6rem 0 0;
      min-height: 90vh;
    }
  
    .hero h1 {
      font-size: 2.5rem;
      letter-spacing: 2px;
    }
  
    .hero p {
      font-size: 1.1rem;
    }
  
    .welcome-text {
      font-size: 1rem;
      letter-spacing: 2px;
    }
  
    .cta-button {
      padding: 1rem 2rem;
      font-size: 1rem;
    }
  
    .vm-grid {
      grid-template-columns: 1fr;
    }
  
    .values-grid,
    .why-grid {
      grid-template-columns: 1fr;
    }
  
    .section {
      padding: 3rem 0;
    }
  }
  
  @media (max-width: 480px) {
    .hero h1 {
      font-size: 2rem;
    }
    
    .cta-button {
      padding: 1rem 2rem;
      font-size: 0.9rem;
    }
  }
  
 @media (max-width: 1024px) {
  .about-section {
    padding-top: 4rem; /* or test with 9rem */
  }
}